#set(title="查看邮件" + email.subject??)
#@jboltLayout()

#define main()
<div class="jbolt_page">
    <div class="jbolt_page_content">
        <div class="card">
            <div class="card-body">
                <div class="email-preview-content">
                    <div class="email-preview-header">
                        
                        <!-- 按钮操作行 -->
                        <div class="email-actions-row mb-3">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-outline-secondary btn-action" onclick="printEmail()">
                                    <i class="fa fa-print"></i>
                                    <span class="btn-text">打印</span>
                                </button>
                                <button class="btn btn-outline-secondary btn-action" onclick="toggleDisplayMode()" title="切换邮件显示模式">
                                    <i class="fa fa-exchange"></i>
                                    <span class="btn-text">显示模式</span>
                                </button>
                                <button class="btn btn-outline-secondary btn-action" id="darkModeToggle" onclick="toggleDarkMode()">
                                    <i class="fa fa-moon-o"></i>
                                    <span class="btn-text">夜间模式</span>
                                </button>
                                <button class="btn btn-success btn-action" onclick="aiTypesetting()">
                                    <i class="fa fa-magic"></i> 
                                    <span class="btn-text">AI排版</span>
                                </button>
                                <button class="btn btn-primary btn-action" onclick="openTranslation()">
                                    <i class="fa fa-language"></i> 
                                    <span class="btn-text">翻译</span>
                                </button>
                                <button class="btn btn-primary btn-action" onclick="replyEmail('#(email.id)')">
                                    <i class="fa fa-reply"></i>
                                    <span class="btn-text">回复</span>
                                </button>
                                <button class="btn btn-primary btn-action" onclick="replyAllEmail('#(email.id)')">
                                    <i class="fa fa-reply-all"></i>
                                    <span class="btn-text">回复全部</span>
                                </button>
                                <button class="btn btn-secondary btn-action" onclick="forwardEmail('#(email.id)')">
                                    <i class="fa fa-share"></i>
                                    <span class="btn-text">转发</span>
                                </button>
                                <button class="btn btn-info btn-action" onclick="resendEmail('#(email.id)')" title="重新发送此邮件">
                                    <i class="fa fa-paper-plane"></i>
                                    <span class="btn-text">邮件重发</span>
                                </button>
                                <button class="btn btn-danger btn-action" onclick="deleteEmail('#(email.id)')" title="删除此邮件">
                                    <i class="fa fa-trash"></i>
                                    <span class="btn-text">删除邮件</span>
                                </button>
                                <button class="btn btn-warning btn-action" id="forceRefetchBtn" onclick="forceRefetchEmail('#(email.id)')" style="display: none;">
                                    <i class="fa fa-refresh"></i>
                                    <span class="btn-text">强制重新接收</span>
                                </button>

                                <!-- 处理状态按钮 -->
                                #if(email.is_follow_up && email.follow_type==1)
                                <button class="btn btn-sm btn-success btn-action" onclick="event.stopPropagation();toggleProcessStatus('#(email.id)')" data-toggle="tooltip" title="处理人：#(email.follow_up_user_name??)，时间：#(email.follow_up_time??)">
                                    <i class="fa fa-check"></i>
                                    <span class="btn-text">设为未处理</span>
                                </button>
                                #else
                                <button class="btn btn-sm btn-danger btn-action" onclick="event.stopPropagation();toggleProcessStatus('#(email.id)')" data-toggle="tooltip" title="标记为已处理">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    <span class="btn-text">处理邮件</span>
                                </button>
                                #end
                            </div>
                        </div>

                        <!-- 客户链接区域 -->
                        <div class="client-links-row mb-2" id="clientLinksRow" style="display: none;">
                            <div class="client-links-compact">
                                <div id="clientLinksContent">
                                    <!-- 客户链接将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>

                        <!-- 邮件标题行 -->
                        <div class="email-subject-row mb-2">
                            <h3 class="email-subject">#(email.subject??)</h3>
                        </div>
                        
                        <div class="email-info mb-2">
                            <p><strong>发件人：</strong><a href="javascript:void(0)" onclick="viewMessagesByEmail('#(email.fromAddress)')">#(email.fromName??email.fromAddress??)</a></p>
                            <p><strong>收件人：</strong><a href="javascript:void(0)" onclick="viewMessagesByEmail('#(email.toAddress)')">#(email.toName??email.toAddress??)</a></p>
                            #if(email.ccAddress??)
                            <p><strong>抄送：</strong><a href="javascript:void(0)" onclick="viewMessagesByEmail('#(email.ccAddress)')">#(email.ccName??email.ccAddress)</a></p>
                            #end
                            <p><strong>时间：</strong><span class="email-datetime" data-datetime="#(email.sentDate??)">#(email.sentDate??)</span></p>
                        </div>
                        
                        <!-- 附件区域 - 放在邮件信息下方，确保独立显示 -->
                        #if(email.hasAttachments??)
                        <div class="email-attachments mt-1 mb-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="attachment-title">附件</span>
                                <button class="btn btn-sm btn-outline-primary btn-xs" onclick="downloadAllAttachments('#(email.id)')">
                                    <i class="fa fa-download"></i> 下载全部
                                </button>
                            </div>
                            <div class="list-group" id="attachmentList">
                                <!-- 附件列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                        #end
                    </div>
                    <div class="email-preview-body">
                        <!-- 快速接收邮件的加载提示 -->
                        <div id="loadingContent" class="alert alert-info" style="display: none;">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div>
                                    <strong>正在下载邮件内容...</strong><br>
                                    <small class="text-muted">这是一封快速接收的邮件，完整内容正在下载中，请稍候...</small>
                                </div>
                            </div>
                        </div>
                        <div id="originalContent">
                        </div>
                        <div id="translatedContent" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加隐藏的字段来存储邮件信息 -->
<textarea id="rawEmailContent" style="display: none;">#(email.content??)</textarea>
<input type="hidden" id="emailId" value="#(email.id)">
<input type="hidden" id="fetchStatus" value="#(email.fetchStatus??)">
<input type="hidden" id="accountId" value="#(email.accountId??)">

<!-- 添加图片查看器 -->
<div class="image-viewer-modal" id="imageViewerModal">
    <span class="image-viewer-close" onclick="closeImageViewer(); return false;" id="imageViewerClose">&times;</span>
    <span class="image-viewer-nav image-viewer-prev" onclick="changeImage(-1)">&lt;</span>
    <span class="image-viewer-nav image-viewer-next" onclick="changeImage(1)">&gt;</span>
    <div class="image-viewer-content">
        <img class="image-viewer-img" id="imageViewerImg">
    </div>
</div>

#end

#define css()
<style>
    .email-content {
        max-height: 600px;
        overflow-y: auto;
    }

    /* PDF.js 容器样式 */
    .pdf-container {
        height: 500px;
        overflow: hidden;
        position: relative;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    #pdf-viewer {
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    #pdf-viewer .page {
        margin: 10px auto;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .pdf-controls {
        background: #f8f9fa;
        padding: 10px;
        border-bottom: 1px solid #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .pdf-controls button,
    .pdf-controls a {
        margin: 0 5px;
    }

    /* PDF下载按钮样式 */
    .pdf-download-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transition: all 0.2s ease;
    }

    .pdf-download-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .pdf-page-info {
        margin: 0 15px;
    }

    .email-preview-content {
        padding: 0;
        max-width: 100%;
        margin: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .email-preview-header {
        margin: 0 0 5px 0; /* 减小外边距 */
        padding: 8px; /* 减小内边距 */
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 0; /* 移除圆角 */
        flex-shrink: 0;
        /* 移除最大高度限制，允许内容自然展开 */
        overflow-y: visible;
    }

    /* 邮件标题样式 */
    .email-subject-row {
        width: 100%;
    }

    .email-subject {
        margin: 0;
        font-size: 18px;
        color: #333;
        font-weight: 600;
        word-break: break-word;
        line-height: 1.3;
        max-width: 100%;
    }

    /* 按钮操作区域样式 */
    .email-actions-row {
        width: 100%;
    }

    /* 按钮样式优化 */
    .btn-action {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 4px;
        transition: all 0.2s ease;
        white-space: nowrap;
        min-width: auto;
        flex-shrink: 0;
    }

    .btn-action i {
        margin-right: 4px;
        font-size: 12px;
    }

    .btn-action .btn-text {
        display: inline;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .email-subject {
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .btn-action {
            padding: 8px 12px;
            font-size: 12px;
            margin-bottom: 4px;
        }
        
        .btn-action .btn-text {
            display: none; /* 在小屏幕上隐藏文字，只显示图标 */
        }
        
        .btn-action i {
            margin-right: 0;
            font-size: 14px;
        }
        
        .email-actions-row .d-flex {
            justify-content: flex-start;
            gap: 6px !important;
        }
    }

    /* 超小屏幕优化 */
    @media (max-width: 480px) {
        .email-preview-header {
            padding: 6px;
        }
        
        .email-subject {
            font-size: 14px;
            line-height: 1.2;
        }
        
        .btn-action {
            padding: 6px 8px;
            min-width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-action i {
            font-size: 12px;
        }
        
        .email-actions-row .d-flex {
            gap: 4px !important;
        }
    }

    /* 确保flex-wrap和gap正常工作 */
    .d-flex.flex-wrap {
        flex-wrap: wrap !important;
    }
    
    .gap-2 {
        gap: 8px !important;
    }

    /* 按钮悬停效果 */
    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-action:active {
        transform: translateY(0);
    }

    /* 夜间模式激活状态 */
    .btn-action.dark-mode-active {
        background-color: #495057;
        border-color: #495057;
        color: white;
    }

    /* 翻译按钮激活状态 */
    .btn-action.translation-active {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    /* AI排版按钮激活状态 */
    .btn-action.ai-typeset-active {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
        animation: none; /* 停止动画 */
    }

    .btn-action.ai-typeset-active:hover {
        background-color: #138496;
        border-color: #117a8b;
    }

    /* 强制刷新按钮特殊样式 */
    #forceRefetchBtn {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
        }
    }

    /* 加载状态按钮 */
    .btn-action.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .btn-action.loading i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 删除和重发按钮的特殊样式 */
    .btn-action.btn-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        transition: all 0.2s ease;
    }

    .btn-action.btn-info:hover {
        background-color: #17a2b8;
        border-color: #17a2b8;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
        transition: all 0.2s ease;
    }

    /* 按钮禁用状态 */
    .btn-action:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
    }

    .email-preview-header p {
        margin: 2px 0;
        color: #666;
        font-size: 13px;
        display: flex;
        align-items: center;
    }

    .email-preview-body {
        font-size: 14px;
        line-height: 1.3;
        color: #333;
        background-color: #fff;
        border-radius: 0; /* 移除圆角 */
        box-shadow: none; /* 移除阴影 */
        padding: 5px;
        flex: 1;
        overflow-y: auto;
        /* 使用更灵活的高度控制，避免固定高度导致的滚动问题 */
        min-height: 0; /* 允许flex布局正确计算高度 */
        /* 移除固定的最大高度限制，让容器根据内容自然扩展 */
        max-height: none;
        position: relative;
        /* 添加底部padding，确保最后内容可见 */
        padding-bottom: 50px;
        /* 确保滚动条样式正常 */
        scrollbar-width: thin;
        scrollbar-color: #888 #f1f1f1;
    }
    
    /* 自定义滚动条样式 */
    .email-preview-body::-webkit-scrollbar {
        width: 12px;
    }
    
    .email-preview-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 6px;
    }
    
    .email-preview-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 6px;
    }
    
    .email-preview-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    
    /* 确保父容器布局稳定 */
    .email-preview-content {
        display: flex;
        flex-direction: column;
        min-height: 0; /* 重要：允许flex子项收缩 */
    }
    
    .email-preview-header {
        flex-shrink: 0; /* 防止头部被压缩 */
    }
    
    /* 确保内容区域占用剩余空间 */
    .email-preview-body {
        flex: 1 1 auto; /* 允许增长和收缩，占用剩余空间 */
        min-height: 0; /* 重要：允许内容收缩并显示滚动条 */
    }

    .email-info {
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .email-attachments {
        border-top: 1px solid #dee2e6;
        padding-top: 5px;
        margin-top: 5px;
        background-color: #f8f9fa;
        border-radius: 3px;
        padding: 5px;
        /* 确保附件区域可见 */
        position: relative;
        z-index: 2;
    }

    .list-group-item {
        transition: background-color 0.2s;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    /* 翻译内容区域样式 - 与原始内容保持一致 */
    #translatedContent {
        width: 100%;
        min-height: 100%;
        padding: 15px;
        box-sizing: border-box;
        font-size: 15px;
        line-height: 1.5 !important;
        max-height: none !important;
        overflow-y: visible !important;
        background-color: #f8f9fa; /* 翻译内容使用稍微不同的背景色 */
        color: #333;
        /* 确保内容完整显示 */
        overflow: visible !important;
        /* 添加底部margin，确保最后内容可见 */
        margin-bottom: 30px !important;
        /* 确保内容不会被截断 */
        height: auto !important;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-top: 10px;
    }

    /* 修复夜间模式 */
    body.dark-mode #originalContent {
        background-color: #333 !important;
        color: #eee !important;
    }

    body.dark-mode #originalContent a {
        color: #8ab4f8 !important;
    }

    body.dark-mode #originalContent .quoted-text {
        color: #bbb !important;
        border-left-color: #666 !important;
    }

    body.dark-mode #originalContent [style*="border-top"] {
        border-top-color: #555 !important;
    }

    /* 添加图片查看器样式 */
    .image-viewer-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.9);
        overflow: hidden;
    }

    .image-viewer-content {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .image-viewer-img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    .image-viewer-close {
        position: absolute;
        top: 20px;
        right: 30px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        transition: 0.3s;
        cursor: pointer;
        z-index: 1001; /* 确保在最顶层 */
        -webkit-user-select: none; /* Safari兼容性 */
        user-select: none; /* 防止文本选择 */
        background: rgba(0, 0, 0, 0.5); /* 添加背景，增强可见性 */
        border-radius: 50%; /* 圆形背景 */
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }

    .image-viewer-close:hover {
        color: #fff;
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
    }

    .image-viewer-close:active {
        transform: scale(0.95);
    }

    .image-viewer-nav {
        position: absolute;
        color: white;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        -webkit-user-select: none; /* Safari兼容性 */
        user-select: none;
        transition: 0.3s;
    }

    .image-viewer-prev {
        left: 20px;
    }

    .image-viewer-next {
        right: 20px;
    }

    .image-viewer-nav:hover {
        color: #bbb;
    }

    /* 调整卡片布局以充分利用空间 */
    .card {
        height: calc(100vh - 30px);
        display: flex;
        flex-direction: column;
    }

    .card-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 0.75rem; /* 减小内边距以增加内容区域 */
    }

    /* 优化邮件内容区域布局 */
    .jbolt_page {
        height: 100vh;
        padding: 0;
        margin: 0;
        overflow: hidden;
    }
    
    .jbolt_page_content {
        height: 100%;
        padding: 5px;
    }
    
    /* 调整卡片布局以充分利用空间 */
    .card {
        height: 100%;
        margin-bottom: 0;
        display: flex;
        flex-direction: column;
    }
    
    .card-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 0.75rem; /* 减小内边距以增加内容区域 */
    }
    
    /* 调整整体布局以充分利用空间 */
    .email-preview-content {
        padding: 0;
        max-width: 100%;
        margin: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .email-preview-header {
        margin-bottom: 10px;
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 5px;
        flex-shrink: 0;
        /* 减小头部高度，为内容区域留出更多空间 */
        max-height: 400px;
        overflow-y: auto;
    }
    
    .email-preview-body {
        font-size: 14px;
        line-height: 1.3;
        color: #333;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        padding: 10px;
        flex: 1;
        overflow-y: auto;
        /* 使用更灵活的高度控制，避免固定高度导致的滚动问题 */
        min-height: 0; /* 允许flex布局正确计算高度 */
        /* 移除固定最小高度，让容器根据内容自然扩展 */
        padding-bottom: 50px; /* 确保最后内容可见 */
    }

    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 5px;
        margin-top: 5px;
    }

    @media (max-width: 768px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        }
    }

    @media (min-width: 1200px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        }
    }
    .attachment-item {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 4px;
        display: flex;
        flex-direction: column;
        transition: all 0.3s;
        position: relative;
        height: 80px; /* 调整高度为80px，增加高度以适应更多文本 */
        width: 80px; /* 保持宽度为80px */
        overflow: hidden;
        cursor: pointer;
    }
    .attachment-item:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        border-color: #aaa;
    }
    .attachment-item-image {
        padding: 0;
        background-color: #f8f9fa;
        position: relative;
    }
    .attachment-item-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.7) 100%);
        border-radius: 4px;
        z-index: 1;
    }
    .attachment-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-grow: 1;
        position: relative;
        z-index: 2;
    }
    .attachment-preview {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-bottom: 2px;
    }
    .attachment-image-overlay {
        width: 100%;
        height: 100%;
    }
    .attachment-icon {
        font-size: 20px;
        color: #6c757d;
        height: 30px;
        display: flex;
        align-items: center;
        margin-bottom: 2px;
    }
    .attachment-info {
        width: 100%;
        text-align: center;
        padding: 2px 0;
    }
    .attachment-info-image {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 2px;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.6);
    }
    .attachment-name {
        font-size: 9px;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 允许显示2行文本 */
        -webkit-box-orient: vertical;
        line-height: 1.2;
        height: 2.4em; /* 增加高度以容纳2行文本 */
        word-break: break-all; /* 允许在任何字符处换行 */
    }
    .attachment-size {
        font-size: 8px;
        color: #6c757d;
    }
    .attachment-info-image .attachment-size {
        color: rgba(255,255,255,0.8);
    }
    .attachment-actions {
        display: flex;
        justify-content: space-around;
        margin-top: 2px;
        padding-top: 2px;
        border-top: 1px solid #eee;
        position: relative;
        z-index: 3;
        background-color: rgba(255, 255, 255, 0.9);
    }
    .attachment-item-image .attachment-actions {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0,0,0,0.5);
        border-top: none;
        padding: 2px;
        border-radius: 0 0 4px 4px;
        opacity: 0;
        transition: opacity 0.2s;
    }
    .attachment-item-image:hover .attachment-actions {
        opacity: 1;
    }
    .attachment-item-image .btn-outline-secondary,
    .attachment-item-image .btn-outline-primary {
        color: white;
        border-color: white;
        background-color: rgba(0,0,0,0.3);
    }
    .attachment-item-image .btn-outline-secondary:hover,
    .attachment-item-image .btn-outline-primary:hover {
        background-color: rgba(255,255,255,0.2);
    }
    .btn-xs {
        padding: 0.1rem 0.2rem;
        font-size: 0.65rem;
        border-radius: 2px;
        transition: all 0.2s;
    }

    .btn-xs:hover {
        transform: scale(1.05);
    }

    .attachment-title {
        font-weight: bold;
        font-size: 14px;
        margin-right: 10px;
    }

    /* HEIC格式图片预览样式 */
    .heic-preview-container {
        position: relative;
        min-height: 200px;
    }

    .heic-fallback {
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
    }

    .heic-fallback .fa-file-image-o {
        color: #6c757d;
    }

    /* 邮件查看页面样式 */
    .email-container {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    /* 客户链接区域样式 - 紧凑版 */
    .client-links-row {
        margin-bottom: 8px;
    }

    .client-links-compact {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 6px 8px;
        font-size: 12px;
    }

    .client-company-item {
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .client-company-item:last-child {
        margin-bottom: 0;
    }

    .client-company-name {
        font-weight: 500;
        color: #495057;
        font-size: 12px;
        white-space: nowrap;
        min-width: 0;
        flex-shrink: 0;
    }

    .client-company-links {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        flex: 1;
    }

    .client-link-btn {
        padding: 2px 6px;
        font-size: 11px;
        border-radius: 3px;
        text-decoration: none;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 3px;
        font-weight: 500;
        border: none;
        line-height: 1.2;
        white-space: nowrap;
    }

    .client-link-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .client-link-btn i {
        font-size: 10px;
    }

    .client-link-order {
        background: #007bff;
        color: white;
    }

    .client-link-order:hover {
        background: #0056b3;
        color: white;
    }

    .client-link-document {
        background: #28a745;
        color: white;
    }

    .client-link-document:hover {
        background: #1e7e34;
        color: white;
    }

    .client-link-customer {
        background: #ffc107;
        color: #212529;
    }

    .client-link-customer:hover {
        background: #e0a800;
        color: #212529;
    }

    /* 超小屏幕优化 */
    @media (max-width: 768px) {
        .client-company-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }

        .client-company-name {
            font-size: 11px;
        }

        .client-link-btn {
            padding: 2px 4px;
            font-size: 10px;
            gap: 2px;
        }

        .client-link-btn i {
            font-size: 9px;
        }
    }

    /* 极小屏幕 - 单行显示 */
    @media (max-width: 480px) {
        .client-links-compact {
            padding: 4px 6px;
        }

        .client-company-item {
            margin-bottom: 2px;
        }

        .client-company-name {
            font-size: 10px;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .client-company-links {
            gap: 2px;
        }
    }
    
    /* 邮件签名样式 */
    .email-signature-table {
        margin: 10px 0;
        border-collapse: collapse;
        border-spacing: 0;
    }
    
    /* 邮件签名中的垂直分隔线 */
    .email-signature-separator {
        width: 1px !important;
        background-color: #cccccc !important;
        padding: 0 !important;
        margin: 0 10px !important;
    }
    
    /* 修复邮件签名中的垂直分隔线 */
    td[style*="background-color: rgb(0, 0, 1)"],
    td[style*="background-color: black"],
    td[style*="background-color:#000"] {
        width: 1px !important;
        background-color: #000 !important;
        padding: 0 !important;
        margin: 0 10px !important;
    }

    /* 邮件内容解析和视觉区分样式 */
    .email-section {
        margin-bottom: 15px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .current-message {
        background-color: #ffffff !important;
        border: 1px solid #e9ecef;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .different-sender-quoted {
        background-color: #e3f2fd !important;
        border-left: 4px solid #2196f3 !important;
        padding: 15px;
        margin-top: 20px;
        position: relative;
        box-shadow: 0 1px 3px rgba(33, 150, 243, 0.1);
    }

    .different-sender-quoted::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #2196f3, #e3f2fd);
        border-radius: 6px 6px 0 0;
    }

    .same-sender-quoted {
        background-color: #f5f5f5 !important;
        border-left: 4px solid #ccc !important;
        padding: 15px;
        margin-top: 15px;
        color: #666 !important;
        font-style: italic;
    }

    .quoted-sender-info {
        font-size: 0.9em !important;
        color: #1976d2 !important;
        font-weight: 500 !important;
        margin-bottom: 10px !important;
        border-bottom: 1px solid #e3f2fd !important;
        padding-bottom: 5px !important;
        display: flex;
        align-items: center;
    }

    .quoted-sender-info i {
        margin-right: 5px;
        font-size: 0.8em;
    }

    .email-content-text {
        line-height: 1.6;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* 夜间模式下的邮件内容样式 */
    body.dark-mode .current-message {
        background-color: #2d2d2d !important;
        border-color: #444 !important;
        color: #eee !important;
    }

    body.dark-mode .different-sender-quoted {
        background-color: #1a237e !important;
        border-left-color: #3f51b5 !important;
        color: #e8eaf6 !important;
    }

    body.dark-mode .different-sender-quoted::before {
        background: linear-gradient(90deg, #3f51b5, #1a237e);
    }

    body.dark-mode .quoted-sender-info {
        color: #7986cb !important;
        border-bottom-color: #3f51b5 !important;
    }

    body.dark-mode .same-sender-quoted {
        background-color: #333 !important;
        border-left-color: #666 !important;
        color: #bbb !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .email-section {
            padding: 12px !important;
            margin-bottom: 12px;
        }

        .quoted-sender-info {
            font-size: 0.8em !important;
        }

        .different-sender-quoted {
            margin-top: 15px;
        }
    }

    /* 打印样式 */
    @media print {
        .current-message {
            background-color: #fff !important;
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }

        .different-sender-quoted {
            background-color: #f0f0f0 !important;
            border-left: 3px solid #000 !important;
            box-shadow: none !important;
        }

        .different-sender-quoted::before {
            display: none;
        }

        .same-sender-quoted {
            background-color: #f8f8f8 !important;
            border-left: 2px solid #666 !important;
        }

        .quoted-sender-info {
            color: #000 !important;
            border-bottom-color: #000 !important;
        }
    }
</style>
#end

#define js()
<script>
    // 检查PDF.js是否已加载，如果未加载则从本地加载
    if (typeof pdfjsLib === 'undefined') {
        console.log('正在从本地加载PDF.js库...');
        var mainScript = document.createElement('script');
        mainScript.src = '/assets/plugins/pdfjs/pdf.js';
        mainScript.onload = function() {
            console.log('本地PDF.js库加载成功');
            // 加载工作线程
            if (typeof pdfjsLib !== 'undefined') {
                pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
                console.log('本地PDF.js工作线程配置已完成');
            }
        };
        document.head.appendChild(mainScript);
    } else {
        // 如果已加载，确保工作线程路径设置正确
        console.log('PDF.js库已加载，配置本地工作线程...');
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
    }
</script>
<script>
    // 移除此处的PDF.js全局配置，改到文档加载完成后配置

    /**
     * 将服务器时间格式转换为本地化时间显示
     * @param {string} serverDateTime 服务器时间格式，如"24-01-15 10:30"
     * @returns {string} 本地化时间字符串
     */
    function formatDateTimeLocalized(serverDateTime) {
        if (!serverDateTime || serverDateTime.trim() === '') {
            return '';
        }
        
        try {
            // 解析服务器时间格式 "yy-MM-dd HH:mm"
            const parts = serverDateTime.split(' ');
            if (parts.length !== 2) {
                return serverDateTime; // 格式不对，返回原始值
            }
            
            const datePart = parts[0]; // "24-01-15"
            const timePart = parts[1]; // "10:30"
            
            const dateParts = datePart.split('-');
            const timeParts = timePart.split(':');
            
            if (dateParts.length !== 3 || timeParts.length !== 2) {
                return serverDateTime; // 格式不对，返回原始值
            }
            
            // 构建Date对象 (注意：年份需要转换为完整年份)
            const year = parseInt('20' + dateParts[0]); // 24 -> 2024
            const month = parseInt(dateParts[1]) - 1; // 月份从0开始
            const day = parseInt(dateParts[2]);
            const hour = parseInt(timeParts[0]);
            const minute = parseInt(timeParts[1]);
            
            const date = new Date(year, month, day, hour, minute);
            
            // 使用浏览器的语言和时区设置进行本地化
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false // 使用24小时制
            };
            
            // 获取浏览器语言，如果不支持则使用中文
            const locale = navigator.language || 'zh-CN';
            
            return date.toLocaleString(locale, options);
        } catch (error) {
            console.warn('时间格式化失败:', error, '原始时间:', serverDateTime);
            return serverDateTime; // 出错时返回原始值
        }
    }
    
    /**
     * 初始化页面时间本地化
     */
    function initDateTimeLocalization() {
        // 查找所有需要本地化的时间元素
        const dateTimeElements = document.querySelectorAll('.email-datetime');
        
        dateTimeElements.forEach(function(element) {
            const originalDateTime = element.getAttribute('data-datetime');
            if (originalDateTime) {
                const localizedDateTime = formatDateTimeLocalized(originalDateTime);
                element.textContent = localizedDateTime;
                
                // 添加title属性显示原始时间
                element.title = '原始时间: ' + originalDateTime;
            }
        });
    }

    function viewMessagesByEmail(email) {
        let url;
        $.get('admin/emailMessages/getUrl?email=' + email, {}, function (res) {
            if (res.state === 'ok') {
                url = res.data.url;
            }
        });
        if (!url) {
            LayerMsgBox.alert(res.msg || '获取邮箱相关邮件失败');
            return;
        }
        layer.open({
            type: 2,
            title: '查看邮件',
            area: ['100%', '100%'],
            content: url + '?_jb_rqtype_=dialog',
            maxmin: true
        });
    }

    /**
     * 切换邮件处理状态
     */
    function toggleProcessStatus(emailId) {
        // 获取当前邮件的处理状态
        const isFollowUp = '#(email.is_follow_up??)' === 'true';
        const followType = '#(email.follow_type??)';
        const currentStatus = isFollowUp && followType === '1';

        if (currentStatus) {
            // 当前是已处理状态，设为未处理
            layer.confirm('确定要将此邮件设为未处理状态吗？', {
                btn: ['确定', '取消'],
                title: '设为未处理'
            }, function(index) {
                layer.close(index);

                $.ajax({
                    url: 'admin/emailMessages/setUnprocessed',
                    type: 'POST',
                    data: { emailId: emailId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.state === 'ok') {
                            layer.msg('已设为未处理', {icon: 1, time: 1500}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg('操作失败: ' + (response.msg || '未知错误'), {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error setting unprocessed:', error);
                        layer.msg('操作失败: 网络错误', {icon: 2});
                    }
                });
            });
        } else {
            // 当前是未处理状态，设为已处理
            layer.confirm('确定要将此邮件标记为已处理吗？', {
                btn: ['确定', '取消'],
                title: '标记为已处理'
            }, function(index) {
                layer.close(index);

                $.ajax({
                    url: 'admin/emailMessages/setProcessed',
                    type: 'POST',
                    data: { emailId: emailId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.state === 'ok') {
                            layer.msg('已标记为处理', {icon: 1, time: 1500}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg('操作失败: ' + (response.msg || '未知错误'), {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error setting processed:', error);
                        layer.msg('操作失败: 网络错误', {icon: 2});
                    }
                });
            });
        }
    }

    // 全局变量，用于存储附件列表和当前附件索引
    let attachmentsList = [];
    let currentAttachmentIndex = 0;

    $(function () {
        // 初始化图片查看器事件
        initImageViewerEvents();

        // 加载附件列表
        loadAttachments('#(email.id)');

        // 加载客户链接
        loadClientLinks();

        // 初始化PDF.js
        initPdfJs();
        
        // 获取邮件状态
        const emailId = document.getElementById('emailId').value;
        const fetchStatus = document.getElementById('fetchStatus').value;
        const originalContent = document.getElementById('originalContent');
        const loadingContent = document.getElementById('loadingContent');
        const forceRefetchBtn = document.getElementById('forceRefetchBtn');

        // 始终显示强制重新接收按钮，无论邮件状态如何
        // 这样用户可以在遇到邮件内容不完整或附件缺失时强制重新接收
        if (forceRefetchBtn) {
            forceRefetchBtn.style.display = 'inline-block';
        }

        // 检查是否已经设置了夜间模式
        checkDarkModePreference();

        // 检查是否是快速接收的邮件
        if (fetchStatus === '0') {
            console.log('This is a quick fetch email, downloading complete content...');
            // 显示加载提示
            if (loadingContent) {
                loadingContent.style.display = 'block';
            }

            // 获取完整内容
            fetchCompleteContent(emailId);
        } else if(fetchStatus === '2'){
            if (originalContent) {
                originalContent.innerHTML = "该封邮件接收异常，请联系管理员!";
            }
        } else {
            // 处理邮件内容
            displayEmailContent();
        }

        // 绑定附件导航按钮事件
        $(document).on('click', '#prevAttachmentBtn', function(e) {
            e.stopPropagation();
            navigateAttachment('prev');
        });

        $(document).on('click', '#nextAttachmentBtn', function(e) {
            e.stopPropagation();
            navigateAttachment('next');
        });

        // 添加键盘导航支持
        $(document).on('keydown', function(e) {
            if ($('#previewModal').hasClass('show')) {
                if (e.keyCode === 37) { // 左箭头
                    navigateAttachment('prev');
                } else if (e.keyCode === 39) { // 右箭头
                    navigateAttachment('next');
                }
            }
        });
        
        // 自动调整布局
        optimizeLayout();
        
        // 监听窗口大小变化，重新调整布局
        $(window).on('resize', function() {
            optimizeLayout();
        });
        
        // 页面加载时自动收起左侧导航栏，提供更多邮件显示空间
        try {
            // 直接添加收缩样式类
            const navElement = parent.document.querySelector('.jbolt-left-nav');
            const contentElement = parent.document.querySelector('.jbolt-right-content');
            if (navElement) {
                navElement.classList.add('left-nav-mini');
            }
            if (contentElement) {
                contentElement.classList.add('right-content-mini');
            }

            // 如果样式类不起作用，则直接修改样式
            if (navElement) {
                navElement.style.width = '60px';
            }
            if (contentElement) {
                contentElement.style.marginLeft = '60px';
            }

            // 设置导航菜单的收缩状态
            localStorage.setItem('jbolt_left_nav_status', 'collapsed');

            console.log('已自动收起左侧导航栏，提供更多邮件显示空间');
        } catch (e) {
            console.error('自动收起导航栏失败:', e);
        }
    });
    
    /**
     * 初始化PDF.js库
     */
    function initPdfJs() {
        console.log('初始化PDF.js...');
        
        // 检查PDF.js是否已加载
        if (typeof pdfjsLib === 'undefined') {
            console.log('PDF.js未加载，正在动态加载...');
            
            // 动态加载PDF.js
            const script = document.createElement('script');
            script.src = '/assets/plugins/pdfjs/pdf.js';
            script.onload = function() {
                console.log('PDF.js加载成功，配置工作线程...');
                // 配置PDF.js工作线程
                pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
                
                // 设置其他选项
                pdfjsLib.GlobalWorkerOptions.disableAutoFetch = true;
                pdfjsLib.GlobalWorkerOptions.disableStream = true;
                
                console.log('PDF.js初始化完成');
            };
            script.onerror = function() {
                console.error('PDF.js加载失败');
            };
            document.head.appendChild(script);
        } else {
            console.log('PDF.js已加载，配置工作线程...');
            // 配置PDF.js工作线程
            pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
            
            // 设置其他选项
            pdfjsLib.GlobalWorkerOptions.disableAutoFetch = true;
            pdfjsLib.GlobalWorkerOptions.disableStream = true;
            
            console.log('PDF.js初始化完成');
        }
    }

    /**
     * 加载客户链接
     */
    function loadClientLinks() {
        const fromAddress = '#(email.fromAddress??)';
        const toAddress = '#(email.toAddress??)';
        const ccAddress = '#(email.ccAddress??)';

        // 如果没有邮箱地址，不显示客户链接
        if (!fromAddress && !toAddress && !ccAddress) {
            console.log('没有邮箱地址，跳过加载客户链接');
            return;
        }

        const clientLinksRow = document.getElementById('clientLinksRow');
        const clientLinksContent = document.getElementById('clientLinksContent');

        // 显示加载状态
        clientLinksContent.innerHTML = '<div class="text-center py-2"><i class="fa fa-spinner fa-spin"></i> 正在查找相关客户...</div>';
        clientLinksRow.style.display = 'block';

        console.log('开始加载客户链接，邮箱地址:', {fromAddress, toAddress, ccAddress});

        $.ajax({
            url: 'admin/emailMessages/getCompaniesFromEmails',
            type: 'GET',
            data: {
                fromAddress: fromAddress,
                toAddress: toAddress,
                ccAddress: ccAddress
            },
            dataType: 'json',
            timeout: 10000, // 10秒超时
            success: function(response) {
                console.log('客户链接查询响应:', response);

                if (response.state === 'ok' && response.data && response.data.length > 0) {
                    console.log('找到', response.data.length, '个相关公司');
                    displayClientLinks(response.data);
                } else {
                    console.log('未找到相关客户公司');
                    hideClientLinks();
                }
            },
            error: function(xhr, status, error) {
                console.error('加载客户链接失败:', {status, error, responseText: xhr.responseText});

                // 显示错误信息
                clientLinksContent.innerHTML = '<div class="text-muted small"><i class="fa fa-exclamation-triangle"></i> 查找客户信息失败</div>';

                // 3秒后隐藏错误信息
                setTimeout(function() {
                    hideClientLinks();
                }, 3000);
            }
        });
    }

    /**
     * 隐藏客户链接区域
     */
    function hideClientLinks() {
        const clientLinksRow = document.getElementById('clientLinksRow');
        if (clientLinksRow) {
            clientLinksRow.style.display = 'none';
        }
    }

    /**
     * 显示客户链接
     */
    function displayClientLinks(companies) {
        const clientLinksRow = document.getElementById('clientLinksRow');
        const clientLinksContent = document.getElementById('clientLinksContent');

        if (!companies || companies.length === 0) {
            return;
        }

        let html = '';
        companies.forEach(function(company, index) {
            const companyName = escapeHtml(company.name || '未知公司');
            const nickName = escapeHtml(company.nick_name || '');
            const osclubId = company.osclub_id || '';

            html += '<div class="client-company-item">';

            // 显示公司名称（简化版）
            let displayName = companyName;
            if (nickName && nickName !== companyName) {
                displayName = nickName; // 优先显示简称
            }
            // 限制长度，避免占用太多空间
            if (displayName.length > 12) {
                displayName = displayName.substring(0, 12) + '...';
            }
            html += '<span class="client-company-name" title="' + nickName ? ' (' + nickName + ')' : '' + '">' + nickName + ':</span>';

            html += '<div class="client-company-links">';

            let hasLinks = false;

            // 订单页面链接
            if (nickName) {
                html += '<a href="http://360.theolympiastone.com/my/dd?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-order" title="' + companyName + ' - 查看订单信息">';
                html += '<i class="fa fa-shopping-cart"></i>订单';
                html += '</a>';

                // 单证页面链接
                html += '<a href="http://360.theolympiastone.com/my/dzhy?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-document" title="' + companyName + ' - 查看单证信息">';
                html += '<i class="fa fa-file-text"></i>单证';
                html += '</a>';

                hasLinks = true;
            }

            // 客户页面链接
            if (osclubId) {
                html += '<a href="http://360.theolympiastone.com/my/kh/edit?id=' + osclubId + '" target="_blank" class="client-link-btn client-link-customer" title="' + companyName + ' - 查看客户详情">';
                html += '<i class="fa fa-user"></i>客户';
                html += '</a>';

                hasLinks = true;
            }

            // 如果没有可用链接，显示提示
            if (!hasLinks) {
                html += '<span class="text-muted" style="font-size: 10px;">无链接</span>';
            }

            html += '</div>';
            html += '</div>';
        });

        clientLinksContent.innerHTML = html;
        clientLinksRow.style.display = 'block';

        // 添加点击统计
        addLinkClickTracking();
    }

    /**
     * 转义HTML字符
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 添加链接点击统计
     */
    function addLinkClickTracking() {
        const links = document.querySelectorAll('.client-link-btn');
        links.forEach(function(link) {
            link.addEventListener('click', function() {
                const linkType = this.classList.contains('client-link-order') ? '订单页面' :
                               this.classList.contains('client-link-document') ? '单证页面' :
                               this.classList.contains('client-link-customer') ? '客户页面' : '未知';

                console.log('用户点击了客户链接:', linkType, this.href);

                // 可以在这里添加统计代码
                // 例如发送统计数据到后端
            });
        });
    }

    /**
     * 优化布局，根据窗口大小调整邮件内容区域
     */
    function optimizeLayout() {
        // 获取窗口高度和宽度
        const windowHeight = window.innerHeight;
        const windowWidth = window.innerWidth;
        
        // 获取邮件头部高度
        const headerHeight = $('.email-preview-header').outerHeight();
        
        // 计算邮件内容区域的最大高度 - 但不强制设置固定高度
        const maxContentHeight = windowHeight - headerHeight - 40; // 为边距和滚动条留出更多空间
        
        // 不再强制设置固定高度，而是设置合理的最大高度，让内容自然滚动
        $('.email-preview-body').css({
            'max-height': maxContentHeight + 'px',
            'height': 'auto' // 让高度自适应内容
        });
        
        // 设置内容区域的宽度为窗口宽度
        $('.email-preview-content').css('width', windowWidth + 'px');
        
        // 如果窗口宽度大于1200px，限制内容最大宽度
        if (windowWidth > 1200) {
            $('.email-preview-content').css('max-width', '100%');
        } else {
            // 对于小屏幕，使用全宽
            $('.email-preview-content').css('max-width', '100%');
        }
        
        // 移除对原始内容区域的最小高度强制设置，让其自然适应内容
        $('#originalContent').css({
            'min-height': 'auto',
            'height': 'auto'
        });
        
        // 确保卡片和卡片内容区域填满整个页面
        $('.card').css('height', '100%');
        $('.card-body').css('padding', '0');
        
        // 自动收起左侧导航栏，提供更多邮件显示空间
        try {
            // 直接添加收缩样式类
            const navElement = parent.document.querySelector('.jbolt-left-nav');
            const contentElement = parent.document.querySelector('.jbolt-right-content');
            if (navElement) {
                navElement.classList.add('left-nav-mini');
            }
            if (contentElement) {
                contentElement.classList.add('right-content-mini');
            }

            // 如果样式类不起作用，则直接修改样式
            if (navElement) {
                navElement.style.width = '60px';
            }
            if (contentElement) {
                contentElement.style.marginLeft = '60px';
            }

            // 设置导航菜单的收缩状态
            localStorage.setItem('jbolt_left_nav_status', 'collapsed');
        } catch (e) {
            console.error('自动收起导航栏失败:', e);
        }
        
        console.log('优化布局：窗口尺寸 =', windowWidth, 'x', windowHeight, '头部高度 =', headerHeight, '最大内容高度 =', maxContentHeight);
    }

    /**
     * 获取邮件完整内容
     */
    function fetchCompleteContent(emailId) {
        $.ajax({
            url: 'admin/emailMessages/fetchCompleteContent',
            type: 'GET',
            data: { emailId: emailId },
            dataType: 'json',
            success: function(response) {
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('Error fetching complete content:', error);
                // 显示错误提示
                document.getElementById('loadingContent').innerHTML =
                    '<div class="alert alert-danger">\n' +
                    '    <strong>下载邮件内容失败</strong><br>\n' +
                    '    <small>网络错误，请稍后重试</small>\n' +
                    '    <button class="btn btn-sm btn-primary mt-2" onclick="retryFetchContent(\'' + emailId + '\')">重试</button>\n' +
                    '</div>';
            }
        });
    }

    /**
     * 重试获取完整内容
     */
    function retryFetchContent(emailId) {
        // 重置加载提示
        document.getElementById('loadingContent').innerHTML =
            '<div class="d-flex align-items-center">\n' +
            '    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">\n' +
            '        <span class="visually-hidden">加载中...</span>\n' +
            '    </div>\n' +
            '    <div>\n' +
            '        <strong>正在下载邮件内容...</strong><br>\n' +
            '        <small class="text-muted">这是一封快速接收的邮件，完整内容正在下载中，请稍候...</small>\n' +
            '    </div>\n' +
            '</div>';
        document.getElementById('loadingContent').style.display = 'block';

        // 重新获取
        fetchCompleteContent(emailId);
    }

    /**
     * 强制重新接收邮件
     * 即使邮件已经完整接收，也强制重新接收
     */
    function forceRefetchEmail(emailId) {
        // 显示确认对话框
        layer.confirm('您确定要强制重新接收这封邮件吗？<br>这将会重新从邮件服务器下载完整内容和附件。', {
            btn: ['确定', '取消'],
            title: '强制重新接收'
        }, function(index) {
            layer.close(index);

            // 显示加载提示
            const loadingContent = document.getElementById('loadingContent');
            loadingContent.innerHTML =
                '<div class="d-flex align-items-center">\n' +
                '    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">\n' +
                '        <span class="visually-hidden">加载中...</span>\n' +
                '    </div>\n' +
                '    <div>\n' +
                '        <strong>正在强制重新接收邮件...</strong><br>\n' +
                '        <small class="text-muted">正在从邮件服务器重新下载完整内容和附件，请稍候...</small>\n' +
                '    </div>\n' +
                '</div>';
            loadingContent.style.display = 'block';

            // 调用强制重新接收的API
            $.ajax({
                url: 'admin/emailMessages/fetchCompleteContent',
                type: 'GET',
                data: { emailId: emailId },
                dataType: 'json',
                success: function(response) {
                    if (response.state === 'ok') {
                        // 成功后刷新页面
                        layer.msg('强制重新接收成功，页面将刷新', {icon: 1, time: 1500}, function() {
                            location.reload();
                        });
                    } else {
                        // 显示错误信息
                        layer.msg('强制重新接收失败: ' + (response.msg || '未知错误'), {icon: 2});
                        loadingContent.style.display = 'none';
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error force refetching email:', error);
                    // 显示错误提示
                    layer.msg('强制重新接收失败: 网络错误', {icon: 2});
                    loadingContent.style.display = 'none';
                }
            });
        });
    }

    /**
     * 显示邮件内容
     */
    function displayEmailContent() {
        const originalContent = document.getElementById('originalContent');
        if (originalContent) {
            console.log('Processing email content...');
            // 使用隐藏的 textarea 来存储原始内容
            const rawContent = document.getElementById('rawEmailContent').value;

            // 添加加载指示器
            originalContent.innerHTML = '<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary me-2"></div> 正在处理邮件内容...</div>';

            // 使用setTimeout来避免浏览器卡顿
            setTimeout(() => {
                try {
                    // 首先检查并安全解码HTML实体
                    let processedContent = rawContent;
                    if (containsHtmlEntities(rawContent)) {
                        console.log('Content contains HTML entities, decoding safely...');
                        console.log('Original content preview:', rawContent.substring(0, 200) + '...');
                        processedContent = safeDecodeHtmlEntities(rawContent);
                        console.log('Decoded content preview:', processedContent.substring(0, 200) + '...');
                        console.log('HTML entities safely decoded');
                    }

                    if (isHtmlContent(processedContent)) {
                        console.log('Content is HTML format');

                        // 智能选择显示方式
                        if (shouldUseIframe(processedContent)) {
                            console.log('Using iframe for complex HTML content');
                            displayHtmlContentInIframe(processedContent, originalContent);
                        } else {
                            console.log('Using direct display for simple HTML content');
                            // 对于简单的HTML内容，仍使用直接显示方式
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = processedContent;
                            cleanEmailContent(tempDiv);
                            originalContent.innerHTML = tempDiv.innerHTML;
                            processImagesInDom(originalContent);
                            handleExternalImages();
                        }
                    } else {
                        console.log('Content is plain text format');
                        // 如果是纯文本，直接格式化显示
                        originalContent.innerHTML = formatPlainTextEmail(processedContent);
                        // 处理纯文本中的外部图片
                        handleExternalImages();
                    }

                    // 重新优化布局
                    optimizeLayout();
                } catch (error) {
                    console.error('处理邮件内容时出错:', error);

                    // 尝试使用原始内容作为备用方案
                    let fallbackContent = rawContent;
                    try {
                        // 如果原始内容包含HTML实体，尝试基础解码
                        if (containsHtmlEntities(rawContent)) {
                            console.log('尝试使用基础HTML实体解码作为备用方案...');
                            fallbackContent = decodeHtmlEntities(rawContent);
                        }
                    } catch (fallbackError) {
                        console.error('备用解码方案也失败:', fallbackError);
                    }

                    originalContent.innerHTML = `<div class="alert alert-warning">
                        <strong>邮件内容处理遇到问题，已使用备用显示方式</strong><br>
                        <small>错误信息: ${error.message || '未知错误'}</small>
                    </div>
                    <div class="mt-3">${fallbackContent}</div>`;
                }
            }, 100);
        } else {
            console.error('Original content container not found');
        }
    }

    /**
     * 在iframe中显示HTML邮件内容
     */
    function displayHtmlContentInIframe(htmlContent, container) {
        // 创建iframe元素
        const iframe = document.createElement('iframe');
        iframe.id = 'emailContentIframe';
        iframe.style.width = '100%';
        iframe.style.border = 'none';
        iframe.style.minHeight = '400px';
        iframe.sandbox = 'allow-same-origin allow-scripts allow-popups allow-forms';

        // 清空容器并添加iframe
        container.innerHTML = '';
        container.appendChild(iframe);

        // 准备iframe内容
        const processedHtml = prepareHtmlForIframe(htmlContent);

        // 写入iframe内容
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(processedHtml);
        iframeDoc.close();

        // 等待iframe加载完成后调整高度
        iframe.onload = function() {
            adjustIframeHeight(iframe);
            // 处理iframe中的图片点击事件
            setupIframeImageHandlers(iframe);
            // 处理外部图片
            handleExternalImagesInIframe(iframe);
        };

        // 存储iframe引用，供打印等功能使用
        window.emailContentIframe = iframe;
    }

    /**
     * 为iframe准备HTML内容
     */
    function prepareHtmlForIframe(htmlContent) {
        // 基本的HTML清理，移除危险脚本但保留样式
        let cleanedHtml = htmlContent
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除脚本
            .replace(/javascript:/gi, '') // 移除javascript协议
            .replace(/on\w+\s*=/gi, ''); // 移除事件处理器

        // 处理所有链接，确保在新标签页中打开
        cleanedHtml = cleanedHtml.replace(/<a\s+([^>]*?)>/gi, function(match, attributes) {
            // 检查是否已经有target属性
            if (/target\s*=/i.test(attributes)) {
                // 如果已有target属性，替换为_blank
                return '<a ' + attributes.replace(/target\s*=\s*["']?[^"'\s]*["']?/gi, 'target="_blank"') + '>';
            } else {
                // 如果没有target属性，添加target="_blank"和rel="noopener noreferrer"
                let newAttributes = attributes;
                if (!/rel\s*=/i.test(attributes)) {
                    newAttributes += ' rel="noopener noreferrer"';
                }
                return '<a ' + newAttributes + ' target="_blank">';
            }
        });

        // 处理CID图片引用
        cleanedHtml = processCidImagesForIframe(cleanedHtml);

        // 解析和处理引用内容
        cleanedHtml = parseAndStyleHtmlEmailContent(cleanedHtml);

        // 如果内容没有完整的HTML结构，添加基本结构
        if (!cleanedHtml.toLowerCase().includes('<html')) {
            cleanedHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                            font-size: 14px;
                            line-height: 1.6;
                            color: #333;
                            margin: 0;
                            padding: 15px;
                            word-wrap: break-word;
                            overflow-wrap: break-word;
                        }
                        img {
                            max-width: 100% !important;
                            height: auto !important;
                            cursor: pointer;
                        }
                        table {
                            border-collapse: collapse;
                            max-width: 100%;
                        }
                        td, th {
                            padding: 4px 8px;
                            border: 1px solid #ddd;
                            word-break: break-word;
                        }
                        blockquote {
                            margin: 1em 0;
                            padding: 10px 0 10px 15px;
                            border-left: 3px solid #ccc;
                            color: #666;
                            background-color: #f9f9f9;
                        }
                        a {
                            color: #007bff;
                            text-decoration: none;
                        }
                        a:hover {
                            text-decoration: underline;
                        }
                        /* 邮件内容解析样式 */
                        .email-section {
                            margin-bottom: 15px;
                            border-radius: 6px;
                            transition: all 0.2s ease;
                        }
                        .current-message {
                            background-color: #ffffff !important;
                            border: 1px solid #e9ecef;
                            padding: 15px;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                        }
                        .different-sender-quoted {
                            background-color: #e3f2fd !important;
                            border-left: 4px solid #2196f3 !important;
                            padding: 15px;
                            margin-top: 20px;
                            position: relative;
                            box-shadow: 0 1px 3px rgba(33, 150, 243, 0.1);
                        }
                        .different-sender-quoted::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 2px;
                            background: linear-gradient(90deg, #2196f3, #e3f2fd);
                            border-radius: 6px 6px 0 0;
                        }
                        .same-sender-quoted {
                            background-color: #f5f5f5 !important;
                            border-left: 4px solid #ccc !important;
                            padding: 15px;
                            margin-top: 15px;
                            color: #666 !important;
                            font-style: italic;
                        }
                        .quoted-sender-info {
                            font-size: 0.9em !important;
                            color: #1976d2 !important;
                            font-weight: 500 !important;
                            margin-bottom: 10px !important;
                            border-bottom: 1px solid #e3f2fd !important;
                            padding-bottom: 5px !important;
                            display: flex;
                            align-items: center;
                        }
                    </style>
                </head>
                <body>
                    ${cleanedHtml}
                </body>
                </html>
            `;
        }

        return cleanedHtml;
    }

    /**
     * 解析和样式化HTML邮件内容中的引用部分
     */
    function parseAndStyleHtmlEmailContent(htmlContent) {
        if (!htmlContent) return htmlContent;

        // 获取当前邮件的发件人信息
        const currentSender = '#(email.fromAddress??)';

        // 创建临时DOM元素来解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // 查找常见的引用分隔符和结构
        const quotedElements = [];

        // 查找blockquote元素
        const blockquotes = tempDiv.querySelectorAll('blockquote');
        blockquotes.forEach(blockquote => {
            quotedElements.push({
                element: blockquote,
                type: 'blockquote',
                sender: extractSenderFromElement(blockquote)
            });
        });

        // 查找包含引用标识的div元素
        const divs = tempDiv.querySelectorAll('div');
        divs.forEach(div => {
            const text = div.textContent || '';
            const innerHTML = div.innerHTML || '';

            // 检查是否包含引用标识
            if (isQuotedContent(text, innerHTML)) {
                quotedElements.push({
                    element: div,
                    type: 'quoted-div',
                    sender: extractSenderFromElement(div)
                });
            }
        });

        // 应用样式到识别的引用元素
        quotedElements.forEach(item => {
            const isDifferentSender = item.sender && item.sender !== 'unknown' && item.sender !== currentSender;
            applyQuotedStyleToElement(item.element, isDifferentSender, item.sender);
        });

        return tempDiv.innerHTML;
    }

    /**
     * 检查文本内容是否为引用内容
     */
    function isQuotedContent(text, innerHTML) {
        const quotedPatterns = [
            /^(>|\|)/m, // 以>或|开头的行
            /From:\s*.+/i,
            /发件人:\s*.+/i,
            /Sent:\s*.+/i,
            /发送时间:\s*.+/i,
            /To:\s*.+/i,
            /收件人:\s*.+/i,
            /Subject:\s*.+/i,
            /主题:\s*.+/i,
            /Original Message/i,
            /原始邮件/i,
            /On\s+.+wrote:/i,
            /在\s+.+写道:/i,
            /^\d{4}年\d{1,2}月\d{1,2}日.*写道：/m
        ];

        return quotedPatterns.some(pattern => pattern.test(text)) ||
               innerHTML.includes('border-left') && (innerHTML.includes('color:#') || innerHTML.includes('color: #'));
    }

    /**
     * 从元素中提取发件人信息
     */
    function extractSenderFromElement(element) {
        const text = element.textContent || '';
        const innerHTML = element.innerHTML || '';

        const senderPatterns = [
            /From:\s*([^<\n]+)(?:<([^>]+)>)?/i,
            /发件人:\s*([^<\n]+)(?:<([^>]+)>)?/i,
            /<([^@\s]+@[^@\s]+\.[^@\s]+)>/,
            /([^@\s]+@[^@\s]+\.[^@\s]+)/
        ];

        for (const pattern of senderPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[2] || match[1] || 'unknown';
            }
        }

        return 'unknown';
    }

    /**
     * 为元素应用引用样式
     */
    function applyQuotedStyleToElement(element, isDifferentSender, sender) {
        // 移除现有的引用相关类
        element.classList.remove('email-section', 'current-message', 'different-sender-quoted', 'same-sender-quoted');

        // 添加基础类
        element.classList.add('email-section');

        if (isDifferentSender) {
            element.classList.add('different-sender-quoted');

            // 添加发件人信息
            if (sender && sender !== 'unknown') {
                const senderInfo = document.createElement('div');
                senderInfo.className = 'quoted-sender-info';
                senderInfo.innerHTML = `<i class="fa fa-reply" style="margin-right: 5px;"></i>来自: ${sender}`;
                element.insertBefore(senderInfo, element.firstChild);
            }
        } else {
            element.classList.add('same-sender-quoted');
        }
    }

    /**
     * 为直接显示的HTML内容应用引用内容样式化
     */
    function applyQuotedContentStyling(container) {
        if (!container) return;

        // 获取当前邮件的发件人信息
        const currentSender = '#(email.fromAddress??)';

        // 查找并处理引用元素
        const quotedElements = [];

        // 查找blockquote元素
        const blockquotes = container.querySelectorAll('blockquote');
        blockquotes.forEach(blockquote => {
            quotedElements.push({
                element: blockquote,
                type: 'blockquote',
                sender: extractSenderFromElement(blockquote)
            });
        });

        // 查找包含引用标识的div元素
        const divs = container.querySelectorAll('div');
        divs.forEach(div => {
            const text = div.textContent || '';
            const innerHTML = div.innerHTML || '';

            // 检查是否包含引用标识
            if (isQuotedContent(text, innerHTML)) {
                quotedElements.push({
                    element: div,
                    type: 'quoted-div',
                    sender: extractSenderFromElement(div)
                });
            }
        });

        // 查找包含引用标识的p元素
        const paragraphs = container.querySelectorAll('p');
        paragraphs.forEach(p => {
            const text = p.textContent || '';
            const innerHTML = p.innerHTML || '';

            // 检查是否包含引用标识
            if (isQuotedContent(text, innerHTML)) {
                quotedElements.push({
                    element: p,
                    type: 'quoted-p',
                    sender: extractSenderFromElement(p)
                });
            }
        });

        // 应用样式到识别的引用元素
        quotedElements.forEach(item => {
            const isDifferentSender = item.sender && item.sender !== 'unknown' && item.sender !== currentSender;
            applyQuotedStyleToElement(item.element, isDifferentSender, item.sender);
        });

        // 为未被识别为引用的主要内容添加当前消息样式
        const mainContentElements = container.children;
        for (let i = 0; i < mainContentElements.length; i++) {
            const element = mainContentElements[i];
            if (!element.classList.contains('email-section')) {
                // 检查是否是主要内容（不是引用内容）
                const text = element.textContent || '';
                const innerHTML = element.innerHTML || '';

                if (!isQuotedContent(text, innerHTML) && text.trim().length > 0) {
                    element.classList.add('email-section', 'current-message');
                }
            }
        }
    }

    /**
     * 调整iframe高度以适应内容
     */
    function adjustIframeHeight(iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const body = iframeDoc.body;
            const html = iframeDoc.documentElement;

            // 获取内容的实际高度
            const height = Math.max(
                body.scrollHeight,
                body.offsetHeight,
                html.clientHeight,
                html.scrollHeight,
                html.offsetHeight
            );

            // 设置iframe高度，最小400px
            iframe.style.height = Math.max(height + 20, 400) + 'px';
        } catch (error) {
            console.error('调整iframe高度失败:', error);
            iframe.style.height = '600px'; // 设置默认高度
        }
    }

    /**
     * 设置iframe中图片的点击处理
     */
    function setupIframeImageHandlers(iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const images = iframeDoc.querySelectorAll('img');

            // 存储图片信息供图片查看器使用
            window.emailImages = [];

            images.forEach((img, index) => {
                // 存储图片信息
                window.emailImages.push({
                    src: img.src,
                    index: index
                });

                // 设置点击预览
                img.onclick = function() {
                    // 调用父窗口的图片查看器
                    window.parent.openImageViewer(index);
                };

                // 添加悬停效果
                img.style.cursor = 'pointer';
                img.title = '点击查看大图';
            });
        } catch (error) {
            console.error('设置iframe图片处理失败:', error);
        }
    }

    /**
     * 处理iframe中的外部图片
     */
    function handleExternalImagesInIframe(iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const externalImages = iframeDoc.querySelectorAll('img[src^="http"], img[src^="https"]');

            if (externalImages.length > 0) {
                console.log(`发现 ${externalImages.length} 张外部图片，默认加载显示`);

                // 外部图片默认加载显示，不需要隐藏
                externalImages.forEach(img => {
                    // 为外部图片添加标识，但保持显示
                    img.setAttribute('data-external-image', 'true');
                    img.style.cursor = 'pointer';
                    img.title = '外部图片 - 点击查看大图';
                });

                // 可选：添加一个不显眼的提示信息
                const infoDiv = iframeDoc.createElement('div');
                infoDiv.style.cssText = `
                    background-color: #d1ecf1;
                    border: 1px solid #bee5eb;
                    color: #0c5460;
                    padding: 8px;
                    margin: 10px 0;
                    border-radius: 4px;
                    font-size: 12px;
                    opacity: 0.8;
                `;
                infoDiv.innerHTML = `
                    <i class="fa fa-info-circle"></i> 此邮件包含 ${externalImages.length} 张外部图片，已自动加载显示。
                `;

                // 将提示插入到body的开头
                const body = iframeDoc.body;
                body.insertBefore(infoDiv, body.firstChild);

                // 3秒后自动隐藏提示
                setTimeout(() => {
                    if (infoDiv.parentNode) {
                        infoDiv.style.transition = 'opacity 0.5s';
                        infoDiv.style.opacity = '0';
                        setTimeout(() => {
                            if (infoDiv.parentNode) {
                                infoDiv.parentNode.removeChild(infoDiv);
                            }
                        }, 500);
                    }
                }, 3000);
            }
        } catch (error) {
            console.error('处理iframe外部图片失败:', error);
        }
    }

    /**
     * 加载iframe中的外部图片
     */
    function loadExternalImagesInIframe() {
        try {
            const iframe = window.emailContentIframe;
            if (!iframe) return;

            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const externalImages = iframeDoc.querySelectorAll('img[data-external-src]');

            externalImages.forEach(img => {
                img.src = img.getAttribute('data-external-src');
                img.style.display = '';
                img.removeAttribute('data-external-src');
            });

            // 移除警告
            const warning = iframeDoc.querySelector('div[style*="background-color: #fff3cd"]');
            if (warning) warning.remove();

            // 重新调整iframe高度
            setTimeout(() => adjustIframeHeight(iframe), 500);
        } catch (error) {
            console.error('加载外部图片失败:', error);
        }
    }

    /**
     * 处理iframe中的CID图片引用
     */
    function processCidImagesForIframe(htmlContent) {
        try {
            // 获取当前邮件的附件信息，用于CID映射
            const emailId = document.getElementById('emailId').value;

            // 替换CID引用为实际的文件路径
            // 这里需要根据后端的CID处理逻辑来调整
            htmlContent = htmlContent.replace(/src=["']cid:([^"']+)["']/gi, function(match, cid) {
                // 构建附件访问URL
                return `src="/admin/emailMessages/attachment?emailId=${emailId}&cid=${cid}"`;
            });

            // 处理CSS中的CID引用
            htmlContent = htmlContent.replace(/url\(['"]?cid:([^'"]+)['"]?\)/gi, function(match, cid) {
                return `url('/admin/emailMessages/attachment?emailId=${emailId}&cid=${cid}')`;
            });

            return htmlContent;
        } catch (error) {
            console.error('处理CID图片失败:', error);
            return htmlContent;
        }
    }

    /**
     * 检查是否需要使用iframe显示
     * 可以根据内容复杂度或用户设置来决定
     */
    function shouldUseIframe(htmlContent) {
        // 默认使用iframe显示邮件，提供更好的隔离和显示效果
        // 只有在特殊情况下才使用直接显示模式

        // 检查是否为纯文本内容（没有HTML标签）
        const isPlainText = !htmlContent.includes('<') && !htmlContent.includes('>');

        // 检查是否为非常简单的HTML（只包含基本格式标签）
        const isVerySimpleHtml = htmlContent.length < 500 &&
            !htmlContent.includes('<style') &&
            !htmlContent.includes('<script') &&
            !htmlContent.includes('<table') &&
            !htmlContent.includes('position:') &&
            !htmlContent.includes('float:') &&
            !/class\s*=/.test(htmlContent) &&
            !/style\s*=/.test(htmlContent);

        // 只有纯文本或非常简单的HTML才不使用iframe
        return !(isPlainText || isVerySimpleHtml);
    }

    /**
     * 切换显示模式（iframe vs 直接显示）
     */
    function toggleDisplayMode() {
        const rawContent = document.getElementById('rawEmailContent').value;
        const originalContent = document.getElementById('originalContent');

        // 首先安全解码HTML实体
        let processedContent = rawContent;
        if (containsHtmlEntities(rawContent)) {
            processedContent = safeDecodeHtmlEntities(rawContent);
        }

        if (!isHtmlContent(processedContent)) {
            layer.msg('纯文本邮件无需切换显示模式', {icon: 0});
            return;
        }

        const currentMode = originalContent.querySelector('#emailContentIframe') ? 'iframe' : 'direct';

        if (currentMode === 'iframe') {
            // 切换到直接显示模式
            originalContent.innerHTML = '<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary me-2"></div> 切换到直接显示模式...</div>';
            setTimeout(() => {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = processedContent;
                cleanEmailContent(tempDiv);
                originalContent.innerHTML = tempDiv.innerHTML;
                processImagesInDom(originalContent);
                handleExternalImages();
                layer.msg('已切换到直接显示模式', {icon: 1});
            }, 100);
        } else {
            // 切换到iframe模式
            originalContent.innerHTML = '<div class="text-center py-3"><div class="spinner-border spinner-border-sm text-primary me-2"></div> 切换到iframe模式...</div>';
            setTimeout(() => {
                displayHtmlContentInIframe(processedContent, originalContent);
                layer.msg('已切换到iframe模式', {icon: 1});
            }, 100);
        }
    }

    /**
     * 彻底清理邮件内容的DOM结构
     * @param {HTMLElement} container - 包含邮件内容的容器元素
     */
    function cleanEmailContent(container) {
        if (!container) return;

        // 首先应用引用内容解析和样式化
        applyQuotedContentStyling(container);

        // 特殊处理blockquote元素，确保引用内容完整显示
        const blockquotes = container.querySelectorAll('blockquote');
        blockquotes.forEach(quote => {
            // 如果blockquote还没有被样式化，应用默认引用样式
            if (!quote.classList.contains('email-section')) {
                quote.classList.add('email-section', 'same-sender-quoted');
            }

            // 确保blockquote元素可以完整显示
            quote.style.maxHeight = 'none';
            quote.style.overflow = 'visible';
            quote.style.whiteSpace = 'normal';
            quote.style.wordBreak = 'break-word';
            quote.style.display = 'block';
            quote.style.width = '100%';

            // 确保blockquote内部的所有元素都可以正常显示
            const allElements = quote.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.style) {
                    el.style.maxHeight = 'none';
                    el.style.overflow = 'visible';
                    el.style.whiteSpace = 'normal';
                    el.style.wordBreak = 'break-word';
                }
            });
        });
        
        // 1. 处理所有图片，确保它们可以显示
        const images = container.querySelectorAll('img');
        images.forEach(img => {
            // 图片内联样式处理
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.margin = '5px 0';
            img.style.display = 'inline-block';
            
            // 移除可能导致图片不显示的属性
            if (img.src && img.src.startsWith('cid:')) {
                // 标记有CID的图片，但不移除src，留给后续处理
                img.setAttribute('data-cid', img.src.substring(4));
            }
        });
        
        // 2. 移除所有注释节点
        const commentNodes = [];
        const iterator = document.createNodeIterator(
            container,
            NodeFilter.SHOW_COMMENT,
            { acceptNode: function() { return NodeFilter.FILTER_ACCEPT; } }
        );
        let currentNode;
        while (currentNode = iterator.nextNode()) {
            commentNodes.push(currentNode);
        }
        commentNodes.forEach(node => node.parentNode.removeChild(node));
        
        // 3. 移除所有空白文本节点
        removeEmptyTextNodes(container);
        
        // 4. 处理Outlook特有标签
        const outlookTags = container.querySelectorAll('o\\:p');
        outlookTags.forEach(tag => tag.parentNode.removeChild(tag));
        
        // 5. 处理所有表格，修复宽度问题
        const tables = container.querySelectorAll('table');
        tables.forEach(table => {
            // 移除可能导致表格超宽的属性
            table.removeAttribute('width');
            table.style.width = '100%';
            table.style.maxWidth = '100%';
            table.style.tableLayout = 'fixed';
            table.style.overflow = 'hidden';
            
            // 处理表格中的单元格
            const cells = table.querySelectorAll('td, th');
            cells.forEach(cell => {
                // 移除固定宽度
                cell.removeAttribute('width');
                cell.style.wordBreak = 'break-word';
                cell.style.whiteSpace = 'normal';
                
                // 如果单元格内容过多，允许溢出并换行
                if (cell.textContent && cell.textContent.length > 100) {
                    cell.style.minWidth = '50px';
                    cell.style.maxWidth = '100%';
                    cell.style.overflow = 'hidden';
                    cell.style.textOverflow = 'ellipsis';
                    cell.style.whiteSpace = 'normal';
                }
            });
        });
        
        // 6. 处理签名表格（通常是位于邮件底部的表格）
        const signatureTables = findSignatureTables(container);
        signatureTables.forEach(table => {
            // 为签名表格添加特殊类以便CSS处理
            table.classList.add('email-signature-table');
            table.style.width = '100%';
            table.style.tableLayout = 'fixed';
            table.style.position = 'static';
            
            // 确保签名表格中的所有定位元素都是静态定位
            const positionedElements = table.querySelectorAll('[style*="position"]');
            positionedElements.forEach(el => {
                el.style.position = 'static';
            });
            
            // 处理签名中的黑色分隔线
            const blackCells = table.querySelectorAll('td[style*="background-color: rgb(0, 0, 1)"], td[style*="background-color: black"], td[style*="background-color:#000"]');
            blackCells.forEach(cell => {
                // 保持黑色背景的单元格样式
                cell.style.backgroundColor = '#000';
                cell.style.width = '1px';
                cell.style.padding = '0';
                cell.style.margin = '0 10px';
                cell.innerHTML = '';
            });
        });
        
        // 7. 改善内容间距，避免内容堆叠
        improveContentSpacing(container);

        // 8. 处理过度嵌套的元素，避免内容重叠
        fixNestedElements(container);

        // 9. 清理空元素
        cleanEmptyElements(container);
        
        // 9. 修复邮件引用中错误的HTML标签
        fixEmailQuoteMarkup(container);
        
        // 10. 优化样式处理，保持更好的布局
        const elements = container.querySelectorAll('*');
        elements.forEach(el => {
            // 移除大部分样式属性，保留少数必要属性
            if (el.style) {
                const color = el.style.color;
                const fontFamily = el.style.fontFamily;
                const fontSize = el.style.fontSize;
                const fontWeight = el.style.fontWeight;
                const backgroundColor = el.style.backgroundColor;
                const textAlign = el.style.textAlign;
                const display = el.style.display;
                const margin = el.style.margin;
                const marginTop = el.style.marginTop;
                const marginBottom = el.style.marginBottom;
                const marginLeft = el.style.marginLeft;
                const marginRight = el.style.marginRight;
                const padding = el.style.padding;
                const paddingTop = el.style.paddingTop;
                const paddingBottom = el.style.paddingBottom;
                const paddingLeft = el.style.paddingLeft;
                const paddingRight = el.style.paddingRight;

                // 保存当前定位方式
                const position = el.style.position;

                // 清空所有样式
                el.removeAttribute('style');

                // 恢复必要样式
                if (color) el.style.color = color;
                if (fontFamily) el.style.fontFamily = fontFamily;
                if (fontSize) el.style.fontSize = fontSize;
                if (fontWeight) el.style.fontWeight = fontWeight;
                if (backgroundColor) el.style.backgroundColor = backgroundColor;
                if (textAlign) el.style.textAlign = textAlign;

                // 恢复合理的间距，避免内容堆叠
                if (margin && margin !== '0px' && margin !== '0') el.style.margin = margin;
                if (marginTop && marginTop !== '0px' && marginTop !== '0') el.style.marginTop = marginTop;
                if (marginBottom && marginBottom !== '0px' && marginBottom !== '0') el.style.marginBottom = marginBottom;
                if (marginLeft && marginLeft !== '0px' && marginLeft !== '0') el.style.marginLeft = marginLeft;
                if (marginRight && marginRight !== '0px' && marginRight !== '0') el.style.marginRight = marginRight;

                if (padding && padding !== '0px' && padding !== '0') el.style.padding = padding;
                if (paddingTop && paddingTop !== '0px' && paddingTop !== '0') el.style.paddingTop = paddingTop;
                if (paddingBottom && paddingBottom !== '0px' && paddingBottom !== '0') el.style.paddingBottom = paddingBottom;
                if (paddingLeft && paddingLeft !== '0px' && paddingLeft !== '0') el.style.paddingLeft = paddingLeft;
                if (paddingRight && paddingRight !== '0px' && paddingRight !== '0') el.style.paddingRight = paddingRight;

                // 如果原来是absolute或fixed定位，强制改为static
                if (position === 'absolute' || position === 'fixed') {
                    el.style.position = 'static';
                } else if (display && display !== 'none') {
                    el.style.display = display;
                }

                // 为没有间距的元素添加默认间距，避免内容堆叠
                const tagName = el.tagName.toLowerCase();
                if (tagName === 'p' && !el.style.marginBottom) {
                    el.style.marginBottom = '0.8em';
                } else if (tagName === 'div' && !el.style.marginBottom && el.textContent.trim()) {
                    el.style.marginBottom = '0.5em';
                } else if (['span', 'a', 'strong', 'b', 'em', 'i'].includes(tagName) && !el.style.marginRight) {
                    // 只为有内容的行内元素添加右边距
                    if (el.textContent.trim()) {
                        el.style.marginRight = '0.2em';
                    }
                } else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
                    if (!el.style.marginTop) el.style.marginTop = '1em';
                    if (!el.style.marginBottom) el.style.marginBottom = '0.5em';
                } else if (tagName === 'br') {
                    // 确保换行符有效果
                    el.style.lineHeight = '1.5';
                }

                // 设置合理的行高，改善可读性
                if (!el.style.lineHeight) {
                    el.style.lineHeight = '1.5';
                }
            }
            
            // 移除多余的类
            if (el.classList.length > 0 && !el.classList.contains('table') && !el.classList.contains('email-signature-table')) {
                el.removeAttribute('class');
            }
            
            // 删除可能导致布局问题的属性
            ['width', 'height', 'valign', 'align', 'border', 'cellspacing', 'cellpadding'].forEach(attr => {
                if (el.hasAttribute(attr)) {
                    el.removeAttribute(attr);
                }
            });
        });
        
        // 11. 处理连续换行
        const brs = container.querySelectorAll('br');
        let lastBr = null;
        brs.forEach(br => {
            if (lastBr && lastBr.nextElementSibling === br) {
                // 发现连续的br，删除当前br
                br.parentNode.removeChild(br);
            } else {
                lastBr = br;
            }
        });
        
        // 12. 处理原始消息分隔线
        const allPs = container.querySelectorAll('p, div');
        allPs.forEach(p => {
            const text = p.textContent || '';
            if (text.includes('Original Message') || 
                text.includes('原始邮件') || 
                text.includes('--------------Original Message') ||
                text.includes('From:') && (text.includes('Sent:') || text.includes('Date:'))) {
                
                p.style.borderTop = '1px solid #ddd';
                p.style.paddingTop = '10px';
                p.style.marginTop = '10px';
                p.style.color = '#666';
                p.style.clear = 'both';  // 确保分隔线前后没有浮动元素
            }
        });
        
        // 13. 确保所有内容可见且不重叠
        container.style.overflow = 'hidden';
        container.style.maxWidth = '100%';
        container.style.position = 'relative';
        
        // 14. 最终再次清理空元素
        cleanEmptyElements(container);
    }
    
    /**
     * 移除所有空白文本节点
     */
    function removeEmptyTextNodes(node) {
        if (!node) return;
        
        for (let i = 0; i < node.childNodes.length; i++) {
            const child = node.childNodes[i];
            
            if (child.nodeType === 3) { // 文本节点
                if (!child.textContent.trim()) {
                    node.removeChild(child);
                    i--; // 调整索引
                }
            } else if (child.nodeType === 1) { // 元素节点
                removeEmptyTextNodes(child);
            }
        }
    }
    
    /**
     * 清理空元素
     */
    function cleanEmptyElements(node) {
        const emptyElements = node.querySelectorAll('p, div, span');
        
        emptyElements.forEach(el => {
            // 检查元素是否为空
            const isEmpty = !el.textContent.trim() && !el.querySelector('img, br, hr');
            
            // 只删除真正空的元素，而不是那些只包含空格或&nbsp;的元素
            // 这样可以避免删除可能包含重要结构的元素
            if (isEmpty && (el.innerHTML === '' || el.innerHTML === '&nbsp;' || el.innerHTML === '<br>')) {
                el.parentNode.removeChild(el);
            }
        });
    }
    
    /**
     * 处理DOM中图片显示问题
     */
    function processImagesInDom(container) {
        const images = container.querySelectorAll('img');
        
        // 存储所有图片的数组，用于图片查看器
        window.emailImages = [];
        
        images.forEach((img, index) => {
            // 存储图片信息
            window.emailImages.push({
                src: img.src,
                index: index
            });
            
            // 设置点击预览
            img.onclick = function() {
                openImageViewer(index);
            };
            
            // 图片加载失败处理
            img.onerror = function() {
                console.log('图片加载失败:', this.src);
                this.style.border = '1px dashed #ccc';
                this.style.padding = '5px';
                this.style.width = '100px';
                this.style.height = '80px';
                this.style.display = 'inline-flex';
                this.style.alignItems = 'center';
                this.style.justifyContent = 'center';
                this.title = '图片加载失败: ' + this.src;
                this.alt = '图片加载失败';
                this.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0iY3VycmVudENvbG9yIiBjbGFzcz0iYmkgYmktaW1hZ2UiIHZpZXdCb3g9IjAgMCAxNiAxNiI+PHBhdGggZD0iTTYuMDAyIDUuNWExLjUgMS41IDAgMSAxLTMgMCAxLjUgMS41IDAgMCAxIDMgMHoiLz48cGF0aCBkPSJNMi4wMDIgMWEyIDIgMCAwIDAtMiAydjEwYTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlYzYTIgMiAwIDAgMC0yLTJoLTEyem0xMiAxYzU1NSAwIDEgLjQ0OCAxIDF2Ni41bC0zLjc3Ny0xLjk0N2EuNS41IDAgMCAwLS41NzcuMDkzbC0zLjcxIDMuNzEtMi42Ni0xLjc3MmEuNS41IDAgMCAwLS41NzcuMDkzbC0zLjY5OSAzLjdhLjUuNSAwIDAgMC0uMTQ2LjM1NFYzYzAtLjU1Mi40NDgtMSAxLTFoMTJ6Ii8+PC9zdmc+';
            };
        });
    }
    
    /**
     * 打开图片查看器
     * @param {number} index - 要显示的图片索引
     */
    function openImageViewer(index) {
        const modal = document.getElementById('imageViewerModal');
        const img = document.getElementById('imageViewerImg');
        
        if (!window.emailImages || window.emailImages.length === 0) {
            return;
        }
        
        // 设置当前图片索引
        window.currentImageIndex = index;
        
        // 设置图片源
        img.src = window.emailImages[index].src;
        
        // 显示模态框
        modal.style.display = 'block';
        
        // 添加键盘事件监听
        document.addEventListener('keydown', handleImageViewerKeyPress);
        
        // 禁止滚动背景
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * 关闭图片查看器
     */
    function closeImageViewer() {
        console.log('closeImageViewer called'); // 调试日志
        const modal = document.getElementById('imageViewerModal');
        if (modal) {
            modal.style.display = 'none';
            console.log('Modal hidden'); // 调试日志
        }

        // 移除键盘事件监听
        document.removeEventListener('keydown', handleImageViewerKeyPress);

        // 恢复滚动
        document.body.style.overflow = '';
    }
    
    /**
     * 切换到下一张或上一张图片
     * @param {number} direction - 方向：1表示下一张，-1表示上一张
     */
    function changeImage(direction) {
        if (!window.emailImages || window.emailImages.length === 0) {
            return;
        }
        
        // 计算新的索引
        let newIndex = window.currentImageIndex + direction;
        
        // 循环浏览
        if (newIndex < 0) {
            newIndex = window.emailImages.length - 1;
        } else if (newIndex >= window.emailImages.length) {
            newIndex = 0;
        }
        
        // 更新当前索引
        window.currentImageIndex = newIndex;
        
        // 更新图片源
        const img = document.getElementById('imageViewerImg');
        img.src = window.emailImages[newIndex].src;
    }
    
    /**
     * 处理图片查看器的键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    function handleImageViewerKeyPress(event) {
        if (event.key === 'Escape') {
            closeImageViewer();
        } else if (event.key === 'ArrowLeft') {
            changeImage(-1);
        } else if (event.key === 'ArrowRight') {
            changeImage(1);
        }
    }
    
    // 点击模态框背景时关闭 - 使用更安全的事件绑定方式
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('imageViewerModal');
        if (modal && event.target === modal) {
            closeImageViewer();
        }
    });

    /**
     * 初始化图片查看器事件
     */
    function initImageViewerEvents() {
        // 确保关闭按钮事件正确绑定
        const closeBtn = document.getElementById('imageViewerClose');
        if (closeBtn) {
            // 移除可能存在的旧事件监听器
            closeBtn.onclick = null;

            // 添加新的事件监听器
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Close button clicked via addEventListener'); // 调试日志
                closeImageViewer();
                return false;
            });

            // 同时保留onclick作为备用
            closeBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Close button clicked via onclick'); // 调试日志
                closeImageViewer();
                return false;
            };
        }


    }

    /**
     * 解析邮件内容，识别引用和历史内容
     */
    function parseEmailContent(content, currentSender) {
        if (!content) return { current: '', quoted: [] };

        // 首先安全解码HTML实体
        if (containsHtmlEntities(content)) {
            content = safeDecodeHtmlEntities(content);
        }

        // 清理并标准化换行符
        content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

        const result = {
            current: '',
            quoted: []
        };

        // 定义各种引用分隔符模式
        const quoteSeparators = [
            // 英文分隔符
            /^[-_]{2,}\s*(Original Message|From:|Sent:|To:|Subject:).*$/gmi,
            /^On\s+.+wrote:$/gmi,
            /^From:\s*.+$/gmi,
            /^Sent:\s*.+$/gmi,
            /^To:\s*.+$/gmi,
            /^Subject:\s*.+$/gmi,
            // 中文分隔符
            /^[-_]{2,}\s*(原始邮件|发件人:|发送时间:|收件人:|主题:).*$/gmi,
            /^在\s+.+写道:$/gmi,
            /^发件人:\s*.+$/gmi,
            /^发送时间:\s*.+$/gmi,
            /^收件人:\s*.+$/gmi,
            /^主题:\s*.+$/gmi,
            // Gmail风格
            /^\d{4}年\d{1,2}月\d{1,2}日.*<.+>.*写道：$/gmi,
            /^On\s+\w+,\s+\w+\s+\d+,\s+\d{4}\s+at\s+\d+:\d+\s+(AM|PM),.*wrote:$/gmi,
            // Outlook风格
            /^From:\s+.+\[mailto:.+\]$/gmi,
            // 其他常见格式
            /^>{1,}\s*.*/gm, // 以>开头的引用行
            /^[\s]*[-]{3,}[\s]*$/gm // 分隔线
        ];

        // 查找第一个引用分隔符的位置
        let firstQuoteIndex = -1;
        let matchedSeparator = null;

        for (const separator of quoteSeparators) {
            const match = content.match(separator);
            if (match) {
                const index = content.indexOf(match[0]);
                if (firstQuoteIndex === -1 || index < firstQuoteIndex) {
                    firstQuoteIndex = index;
                    matchedSeparator = match[0];
                }
            }
        }

        if (firstQuoteIndex !== -1) {
            // 分割当前邮件内容和引用内容
            result.current = content.substring(0, firstQuoteIndex).trim();
            const quotedContent = content.substring(firstQuoteIndex).trim();

            // 解析引用内容，尝试识别不同的发件人
            result.quoted = parseQuotedContent(quotedContent, currentSender);
        } else {
            // 没有找到明显的引用分隔符，检查是否有以>开头的行
            const lines = content.split('\n');
            let currentContent = [];
            let quotedLines = [];
            let inQuoted = false;

            for (const line of lines) {
                if (line.trim().startsWith('>')) {
                    inQuoted = true;
                    quotedLines.push(line);
                } else if (inQuoted && line.trim() === '') {
                    quotedLines.push(line);
                } else if (inQuoted) {
                    // 结束引用部分
                    if (quotedLines.length > 0) {
                        result.quoted.push({
                            content: quotedLines.join('\n'),
                            sender: 'unknown',
                            type: 'quoted'
                        });
                        quotedLines = [];
                    }
                    inQuoted = false;
                    currentContent.push(line);
                } else {
                    currentContent.push(line);
                }
            }

            // 处理剩余的引用内容
            if (quotedLines.length > 0) {
                result.quoted.push({
                    content: quotedLines.join('\n'),
                    sender: 'unknown',
                    type: 'quoted'
                });
            }

            result.current = currentContent.join('\n').trim();
        }

        return result;
    }

    /**
     * 解析引用内容，识别不同发件人的内容
     */
    function parseQuotedContent(quotedContent, currentSender) {
        const quotedSections = [];

        // 尝试按发件人信息分割内容
        const senderPatterns = [
            /From:\s*([^<\n]+)(?:<([^>]+)>)?/gi,
            /发件人:\s*([^<\n]+)(?:<([^>]+)>)?/gi,
            /^\d{4}年\d{1,2}月\d{1,2}日.*<([^>]+)>.*写道：/gmi,
            /On\s+.+,\s*([^<\n]+)\s*<([^>]+)>\s*wrote:/gi
        ];

        let sections = [quotedContent];

        // 尝试按发件人信息分割
        for (const pattern of senderPatterns) {
            const newSections = [];
            for (const section of sections) {
                const matches = [...section.matchAll(pattern)];
                if (matches.length > 0) {
                    let lastIndex = 0;
                    for (const match of matches) {
                        const beforeMatch = section.substring(lastIndex, match.index).trim();
                        if (beforeMatch) {
                            newSections.push({
                                content: beforeMatch,
                                sender: 'unknown',
                                type: 'quoted'
                            });
                        }

                        const afterMatch = section.substring(match.index).trim();
                        const senderEmail = match[2] || match[1];
                        const senderName = match[1];

                        newSections.push({
                            content: afterMatch,
                            sender: senderEmail || senderName || 'unknown',
                            senderName: senderName,
                            type: 'quoted'
                        });

                        lastIndex = section.length;
                    }
                } else {
                    newSections.push({
                        content: section,
                        sender: 'unknown',
                        type: 'quoted'
                    });
                }
            }
            sections = newSections;
        }

        // 如果没有成功分割，将整个内容作为一个引用块
        if (sections.length === 1 && typeof sections[0] === 'string') {
            sections = [{
                content: sections[0],
                sender: 'unknown',
                type: 'quoted'
            }];
        }

        return sections.filter(section => section.content && section.content.trim());
    }

    /**
     * 格式化纯文本邮件内容（增强版）
     */
    function formatPlainTextEmail(content) {
        if (!content) return '';

        // 获取当前邮件的发件人信息
        const currentSender = '#(email.fromAddress??)';

        // 解析邮件内容
        const parsed = parseEmailContent(content, currentSender);

        let formattedContent = '';

        // 格式化当前邮件内容
        if (parsed.current) {
            formattedContent += formatEmailSection(parsed.current, 'current');
        }

        // 格式化引用内容
        if (parsed.quoted && parsed.quoted.length > 0) {
            for (const quotedSection of parsed.quoted) {
                const isDifferentSender = quotedSection.sender !== 'unknown' &&
                                        quotedSection.sender !== currentSender;
                const sectionType = isDifferentSender ? 'different-sender' : 'quoted';
                formattedContent += formatEmailSection(quotedSection.content, sectionType, quotedSection);
            }
        }

        return formattedContent;
    }

    /**
     * 格式化邮件内容段落
     */
    function formatEmailSection(content, type, metadata = {}) {
        if (!content) return '';

        // HTML转义
        let escapedContent = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');

        // 识别链接并转换为可点击的链接
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        escapedContent = escapedContent.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');

        // 移除连续的空行
        escapedContent = escapedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 处理换行和制表符
        escapedContent = escapedContent
            .replace(/\n/g, '<br>')
            .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');

        // 根据类型应用不同的样式
        let className = 'email-section';
        let style = 'font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.5; margin-bottom: 15px;';

        switch (type) {
            case 'current':
                className += ' current-message';
                style += ' padding: 15px; background-color: #ffffff; border-radius: 6px;';
                break;
            case 'different-sender':
                className += ' different-sender-quoted';
                style += ' padding: 15px; background-color: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 6px; margin-top: 20px;';
                break;
            case 'quoted':
                className += ' same-sender-quoted';
                style += ' padding: 15px; background-color: #f5f5f5; border-left: 4px solid #ccc; border-radius: 6px; margin-top: 15px; color: #666;';
                break;
        }

        // 添加发件人信息（如果有）
        let senderInfo = '';
        if (metadata.sender && metadata.sender !== 'unknown' && type === 'different-sender') {
            const senderName = metadata.senderName || metadata.sender;
            senderInfo = `<div class="quoted-sender-info" style="font-size: 0.9em; color: #1976d2; font-weight: 500; margin-bottom: 10px; border-bottom: 1px solid #e3f2fd; padding-bottom: 5px;">
                <i class="fa fa-reply" style="margin-right: 5px;"></i>来自: ${senderName}
            </div>`;
        }

        return `<div class="${className}" style="${style}">
            ${senderInfo}
            <div class="email-content-text">${escapedContent}</div>
        </div>`;
    }



    /**
     * 打印邮件内容
     */
    function printEmail() {
        // 设置打印按钮为加载状态
        const printBtn = document.querySelector('button[onclick*="printEmail"]');
        if (printBtn) {
            printBtn.classList.add('loading');
            printBtn.disabled = true;
            const icon = printBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }
        
        try {
            // 创建一个新窗口用于打印
            const printWindow = window.open('', '_blank');

        // 准备打印内容
        const subject = document.querySelector('.email-preview-header h3').innerText;
        const from = document.querySelector('.email-preview-header p:nth-child(2)').innerText;
        const to = document.querySelector('.email-preview-header p:nth-child(3)').innerText;
        const date = document.querySelector('.email-preview-header p:last-child').innerText;

        // 获取邮件内容，支持iframe和普通内容
        let content = '';
        const iframe = document.getElementById('emailContentIframe');
        if (iframe) {
            // 如果是iframe内容，获取iframe中的内容
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                content = iframeDoc.body.innerHTML;
            } catch (error) {
                console.error('获取iframe内容失败:', error);
                content = '<p>无法获取邮件内容用于打印</p>';
            }
        } else {
            // 普通内容
            content = document.getElementById('originalContent').innerHTML;
        }

        // 检查是否为夜间模式
        const isDarkMode = document.body.classList.contains('dark-mode');

        // 创建打印模板
        const printTemplate = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>打印邮件: ${subject}</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.5;
                        color: ${isDarkMode ? '#eee' : '#333'};
                        padding: 20px;
                        background-color: ${isDarkMode ? '#222' : '#fff'};
                    }
                    .email-header {
                        margin-bottom: 20px;
                        padding-bottom: 10px;
                        border-bottom: 1px solid ${isDarkMode ? '#444' : '#ddd'};
                    }
                    .email-header h2 {
                        margin: 0 0 15px;
                        color: ${isDarkMode ? '#eee' : '#333'};
                    }
                    .email-header p {
                        margin: 5px 0;
                        color: ${isDarkMode ? '#ccc' : '#666'};
                    }
                    .email-content {
                        margin-top: 20px;
                    }
                    .email-content a {
                        color: ${isDarkMode ? '#8ab4f8' : '#007bff'};
                    }
                    .email-content table {
                        border-color: ${isDarkMode ? '#555' : '#dee2e6'};
                    }
                    .email-content th {
                        background-color: ${isDarkMode ? '#444' : '#f8f9fa'};
                        color: ${isDarkMode ? '#eee' : '#333'};
                    }
                    .email-content td {
                        border-color: ${isDarkMode ? '#555' : '#dee2e6'};
                    }
                    .button-container {
                        text-align: center;
                        margin-top: 20px;
                    }
                    .button-container button {
                        padding: 8px 16px;
                        margin: 0 5px;
                        background-color: ${isDarkMode ? '#444' : '#f8f9fa'};
                        color: ${isDarkMode ? '#eee' : '#333'};
                        border: 1px solid ${isDarkMode ? '#666' : '#ddd'};
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    .button-container button:hover {
                        background-color: ${isDarkMode ? '#555' : '#e9ecef'};
                    }
                    @media print {
                        body {
                            padding: 0;
                            background-color: #fff;
                            color: #000;
                        }
                        .email-header {
                            border-bottom: 1px solid #000;
                        }
                        .email-header h2, .email-header p {
                            color: #000;
                        }
                        .button-container {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="email-header">
                    <h2>${subject}</h2>
                    <p>${from}</p>
                    <p>${to}</p>
                    <p>${date}</p>
                </div>
                <div class="email-content">
                    ${content}
                </div>
                <div class="button-container">
                    <button onclick="window.print(); return false;">打印</button>
                    <button onclick="window.close(); return false;">关闭</button>
                </div>
            </body>
            </html>
        `;

            // 写入打印窗口
            printWindow.document.open();
            printWindow.document.write(printTemplate);
            printWindow.document.close();
            
            // 延迟恢复按钮状态
            setTimeout(() => {
                if (printBtn) {
                    printBtn.classList.remove('loading');
                    printBtn.disabled = false;
                    const icon = printBtn.querySelector('i');
                    if (icon) {
                        icon.className = 'fa fa-print';
                    }
                }
            }, 1000);
            
        } catch (error) {
            console.error('打印功能出错:', error);
            layer.msg('打印功能暂时不可用', {icon: 2});
            
            // 恢复按钮状态
            if (printBtn) {
                printBtn.classList.remove('loading');
                printBtn.disabled = false;
                const icon = printBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-print';
                }
            }
        }
    }

    /**
     * 更新打印模板样式
     */
    function updatePrintTemplateStyle() {
        // 如果已经打开了打印窗口，则更新其样式
        // 这个函数仅作为占位符，因为打印窗口是新窗口，无法直接操作
        // 实际上，每次点击打印时都会创建新的打印窗口，并应用当前的模式
    }

    /**
     * 处理邮件中的外部图片
     */
    function handleExternalImages() {
        const originalContent = document.getElementById('originalContent');
        if (!originalContent) return;

        // 首先定义加载所有外部图片的函数，确保它在被调用前已定义
        window.loadAllExternalImages = function() {
            const externalImages = document.querySelectorAll('.external-image');
            externalImages.forEach(img => {
                img.src = img.getAttribute('data-original-src');
                img.style.border = 'none';
                img.style.background = 'none';
                img.style.padding = '0';
                img.title = '';
                img.classList.remove('external-image');
            });

            // 移除警告
            const warning = document.querySelector('#originalContent .alert-warning');
            if (warning) warning.remove();
        };

        // 定义切换自动加载设置的函数
        window.toggleAutoLoadImages = function(enable) {
            localStorage.setItem('autoLoadExternalImages', enable ? 'true' : 'false');

            // 刷新页面以应用新设置
            location.reload();
        };

        // 查找所有图片
        const images = originalContent.querySelectorAll('img');
        let hasExternalImages = false;

        // 默认自动加载外部图片（除非用户明确设置为false）
        const autoLoadImages = localStorage.getItem('autoLoadExternalImages') !== 'false'; // 默认为true，只有明确设置为false才不加载

        images.forEach(img => {
            // 检查是否为外部图片
            if (img.src && !img.src.startsWith(window.location.origin) && !img.src.startsWith('data:')) {
                hasExternalImages = true;

                if (!autoLoadImages) {
                    // 如果用户选择不自动加载，则屏蔽外部图片
                    // 标记外部图片
                    img.classList.add('external-image');
                    img.setAttribute('data-original-src', img.src);
                    // 默认不显示外部图片，防止跟踪
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0iY3VycmVudENvbG9yIiBjbGFzcz0iYmkgYmktaW1hZ2UiIHZpZXdCb3g9IjAgMCAxNiAxNiI+PHBhdGggZD0iTTYuMDAyIDUuNWExLjUgMS41IDAgMSAxLTMgMCAxLjUgMS41IDAgMCAxIDMgMHoiLz48cGF0aCBkPSJNMi4wMDIgMWEyIDIgMCAwIDAtMiAydjEwYTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlYzYTIgMiAwIDAgMC0yLTJoLTEyem0xMiAxYzU1NSAwIDEgLjQ0OCAxIDF2Ni41bC0zLjc3Ny0xLjk0N2EuNS41IDAgMCAwLS41NzcuMDkzbC0zLjcxIDMuNzEtMi42Ni0xLjc3MmEuNS41IDAgMCAwLS41NzcuMDkzbC0zLjY5OSAzLjdhLjUuNSAwIDAgMC0uMTQ2LjM1NFYzYzAtLjU1Mi40NDgtMSAxLTFoMTJ6Ii8+PC9zdmc+';;
                    img.style.padding = '10px';
                    img.style.border = '1px dashed #ccc';
                    img.style.background = '#f8f9fa';
                    img.style.cursor = 'pointer';
                    img.title = '点击加载外部图片';

                    // 添加点击事件加载图片
                    img.onclick = function() {
                        if (this.classList.contains('external-image')) {
                            this.src = this.getAttribute('data-original-src');
                            this.style.border = 'none';
                            this.style.background = 'none';
                            this.style.padding = '0';
                            this.title = '';
                            this.classList.remove('external-image');
                        } else {
                            // 如果已经加载，则执行原来的预览功能
                            previewImage(this.src);
                        }
                    };
                }
            }
        });

        // 如果有外部图片
        if (hasExternalImages) {
            if (autoLoadImages) {
                // 默认自动加载所有外部图片
                console.log('外部图片默认自动加载');
                // 不需要调用loadAllExternalImages()，因为图片已经是正常显示状态

                // 可选：显示一个不显眼的提示信息
                const infoDiv = document.createElement('div');
                infoDiv.className = 'alert alert-info mt-2 mb-2';
                infoDiv.style.fontSize = '12px';
                infoDiv.style.opacity = '0.8';
                infoDiv.innerHTML = `
                    <i class="fa fa-info-circle"></i>
                    此邮件包含外部图片，已自动加载显示。
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="toggleAutoLoadImages(false)">禁用自动加载</button>
                `;
                originalContent.insertBefore(infoDiv, originalContent.firstChild);

                // 3秒后自动隐藏提示
                setTimeout(() => {
                    if (infoDiv.parentNode) {
                        infoDiv.style.transition = 'opacity 0.5s';
                        infoDiv.style.opacity = '0';
                        setTimeout(() => {
                            if (infoDiv.parentNode) {
                                infoDiv.parentNode.removeChild(infoDiv);
                            }
                        }, 500);
                    }
                }, 3000);
            } else {
                // 如果用户设置为不自动加载，显示提示信息
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning mt-3 mb-3';
                warningDiv.innerHTML = `
                    <i class="fa fa-exclamation-triangle"></i>
                    <strong>安全提示：</strong> 此邮件包含外部图片，已被自动屏蔽以保护您的隐私。
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="loadAllExternalImages()">加载所有外部图片</button>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="toggleAutoLoadImages(true)">启用自动加载</button>
                `;
                originalContent.insertBefore(warningDiv, originalContent.firstChild);
            }
        }
    }

    // 优化邮件显示，移除多余空白，增强可读性
    function optimizeEmailDisplay(container) {
        if (!container) return;

        // 移除所有空的元素（更彻底的清理）
        const emptyElements = container.querySelectorAll('p, div, span, br');
        emptyElements.forEach(el => {
            // 检查元素是否为空或只包含空格、&nbsp;等
            const content = el.innerHTML.trim();
            const textContent = el.textContent.trim();
            
            if (content === '' || content === '&nbsp;' || content === ' ' || 
                textContent === '' || /^(\s|&nbsp;)+$/.test(content)) {
                
                // 如果是br元素，直接移除
                if (el.tagName.toLowerCase() === 'br') {
                    el.parentNode.removeChild(el);
                    return;
                }
                
                // 检查是否有子元素
                if (el.children.length === 0 || 
                   (el.children.length === 1 && el.children[0].tagName.toLowerCase() === 'br')) {
                    // 没有子元素或只有一个br子元素，可以安全移除
                    el.parentNode.removeChild(el);
                }
            }
        });

        // 移除连续的<br>标签，保留一个
        const brs = container.querySelectorAll('br');
        for (let i = 0; i < brs.length; i++) {
            let br = brs[i];
            let nextEl = br.nextElementSibling;
            while (nextEl && nextEl.tagName === 'BR') {
                const elToRemove = nextEl;
                nextEl = nextEl.nextElementSibling;
                elToRemove.parentNode.removeChild(elToRemove);
            }
        }

        // 特殊处理连续的空行元素
        const paragraphs = container.querySelectorAll('p, div');
        for (let i = 0; i < paragraphs.length; i++) {
            const p = paragraphs[i];
            if (p.textContent.trim() === '' && 
                (!p.innerHTML.trim() || p.innerHTML.trim() === '&nbsp;' || p.innerHTML.trim() === '<o:p></o:p>')) {
                const prevEl = p.previousElementSibling;
                const nextEl = p.nextElementSibling;
                
                // 如果前后都是空行，移除当前空行
                if (prevEl && nextEl && 
                    prevEl.textContent.trim() === '' && 
                    nextEl.textContent.trim() === '') {
                    p.parentNode.removeChild(p);
                    i--; // 调整索引，因为我们刚移除了一个元素
                }
            }
        }

        // 移除所有多余的样式，特别是空白相关的
        const styledElements = container.querySelectorAll('*[style]');
        styledElements.forEach(el => {
            // 删除可能导致过多空白的样式属性
            el.style.margin = '0';
            el.style.padding = '0';
            el.style.lineHeight = '1.4';
            
            // 移除空元素的前后空白
            if (!el.textContent.trim()) {
                el.style.display = 'none';
            }
        });

        // 处理Outlook特定格式
        const outlookSpecific = container.querySelectorAll('.MsoNormal, .WordSection1, [class^="Word"], [style*="mso-"]');
        outlookSpecific.forEach(el => {
            el.style.margin = '0';
            el.style.padding = '0';
            el.style.lineHeight = '1.4';
            // 移除Outlook特有的样式属性
            el.removeAttribute('class');
        });

        // 优化表格显示
        const tables = container.querySelectorAll('table');
        tables.forEach(table => {
            // 添加Bootstrap表格类
            table.classList.add('table', 'table-sm', 'table-bordered');
            table.style.width = '100%';
            table.style.maxWidth = '100%';
            table.style.marginBottom = '1rem';
            table.style.borderCollapse = 'collapse';

            // 处理表格单元格
            const cells = table.querySelectorAll('td, th');
            cells.forEach(cell => {
                cell.style.padding = '0.5rem';
                cell.style.verticalAlign = 'top';
                cell.style.borderTop = '1px solid #dee2e6';
            });
        });

        // 处理引用文本
        const blockquotes = container.querySelectorAll('blockquote');
        blockquotes.forEach(quote => {
            quote.style.borderLeft = '3px solid #ccc';
            quote.style.paddingLeft = '10px';
            quote.style.color = '#666';
            quote.style.margin = '10px 0';
        });

        // 处理链接
        const links = container.querySelectorAll('a');
        links.forEach(link => {
            // 保留原始链接
            if (!link.getAttribute('data-original-url')) {
                link.setAttribute('data-original-url', link.href);
            }
            // 添加目标属性，在新窗口打开
            link.setAttribute('target', '_blank');
            // 添加安全属性
            link.setAttribute('rel', 'noopener noreferrer');
            // 添加样式
            link.style.color = '#007bff';
            link.style.textDecoration = 'none';
        });

        // 处理图片
        const images = container.querySelectorAll('img');
        images.forEach(img => {
            // 确保图片不会超出容器
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            // 添加点击放大功能
            img.style.cursor = 'pointer';
            img.onclick = function() {
                previewImage(this.src);
            };
            // 添加加载失败处理
            img.onerror = function() {
                this.style.display = 'none';
            };
        });

        // 处理列表
        const lists = container.querySelectorAll('ul, ol');
        lists.forEach(list => {
            list.style.paddingLeft = '20px';
            list.style.marginBottom = '10px';
        });

        // 处理标题
        const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            heading.style.marginTop = '15px';
            heading.style.marginBottom = '10px';
            heading.style.fontWeight = 'bold';
            heading.style.lineHeight = '1.2';
        });

        // 处理签名区域
        const signatures = container.querySelectorAll('.email-signature, .signature');
        signatures.forEach(sig => {
            sig.style.borderTop = '1px solid #eee';
            sig.style.marginTop = '15px';
            sig.style.paddingTop = '10px';
            sig.style.color = '#777';
            sig.style.fontSize = '0.9em';
        });

        return container;
    }

    function loadAttachments(emailId) {
        console.log(emailId + ', 正在加载附件列表...');
        $.ajax({
            url: 'admin/emailMessages/getAttachments',
            type: 'GET',
            data: {emailId: emailId},
            dataType: 'json',
            success: function(res) {
                console.log('附件数据返回:', res);
                if (res.state === 'ok' && res.data) {
                    var attachments = Array.isArray(res.data.data) ? res.data.data : [];
                    console.log('解析到附件数量:', attachments.length);
                    
                    if (attachments.length === 0) {
                        $('#attachmentList').html('<div class="alert alert-info">没有附件</div>');
                        return;
                    }
                    
                    var html = '<div class="attachment-grid">';

                    // 存储附件列表到全局变量
                    attachmentsList = [];

                    attachments.forEach(function (attachment, index) {
                        console.log('处理附件:', attachment);
                        
                        // 检查是否为图片类型（包括无后缀的情况和HEIC格式）
                        const hasExtension = /\.[^.]+$/.test(attachment.fileName);
                        const isImage = hasExtension ?
                            /\.(jpg|jpeg|png|gif|bmp|webp|heic|heif)$/i.test(attachment.fileName) :
                            attachment.contentType && (attachment.contentType.startsWith('image/') ||
                                attachment.contentType === 'image/heic' || attachment.contentType === 'image/heif');

                        // 检查是否为PDF
                        const isPdf = hasExtension ?
                            /\.pdf$/i.test(attachment.fileName) :
                            attachment.contentType === 'application/pdf';

                        // 检查是否为Office文档
                        const isWord = hasExtension ? /\.(doc|docx)$/i.test(attachment.fileName) :
                            attachment.contentType === 'application/msword' ||
                            attachment.contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

                        const isExcel = hasExtension ? /\.(xls|xlsx)$/i.test(attachment.fileName) :
                            attachment.contentType === 'application/vnd.ms-excel' ||
                            attachment.contentType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

                        const isPpt = hasExtension ? /\.(ppt|pptx)$/i.test(attachment.fileName) :
                            attachment.contentType === 'application/vnd.ms-powerpoint' ||
                            attachment.contentType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation';

                        // 检查是否为文本文件
                        const isText = hasExtension ?
                            /\.(txt|log|ini|csv|xml|json|html|htm|css|js)$/i.test(attachment.fileName) :
                            attachment.contentType && (attachment.contentType.startsWith('text/') ||
                                attachment.contentType === 'application/json' ||
                                attachment.contentType === 'application/xml');

                        // 如果是图片但没有后缀，添加.png后缀
                        const displayFileName = isImage && !hasExtension ?
                            `${attachment.fileName}.png` :
                            attachment.fileName;

                        // 文件图标
                        let fileIcon = 'fa-paperclip';
                        if (isImage) fileIcon = 'fa-file-image-o';
                        else if (isPdf) fileIcon = 'fa-file-pdf-o';
                        else if (isWord) fileIcon = 'fa-file-word-o';
                        else if (isExcel) fileIcon = 'fa-file-excel-o';
                        else if (isPpt) fileIcon = 'fa-file-powerpoint-o';
                        else if (isText) fileIcon = 'fa-file-text-o';
                        else if (/\.zip|\.rar|\.7z$/i.test(attachment.fileName)) fileIcon = 'fa-file-archive-o';
                        else if (/\.mp3|\.wav|\.ogg|\.flac$/i.test(attachment.fileName)) fileIcon = 'fa-file-audio-o';
                        else if (/\.mp4|\.avi|\.mov|\.wmv|\.mkv$/i.test(attachment.fileName)) fileIcon = 'fa-file-video-o';
                        else if (/\.html|\.htm|\.xml|\.js|\.css|\.php$/i.test(attachment.fileName)) fileIcon = 'fa-file-code-o';
                        else fileIcon = 'fa-file-o';

                        // 下载链接
                        const downloadUrl = `admin/emailMessages/downloadAttachment?emailId=${emailId}&fileName=${encodeURIComponent(attachment.fileName)}`;

                        // 将附件信息添加到全局列表
                        attachmentsList.push({
                            url: downloadUrl,
                            fileName: displayFileName,
                            originalFileName: attachment.fileName,
                            isImage: isImage,
                            isPdf: isPdf,
                            isWord: isWord,
                            isExcel: isExcel,
                            isPpt: isPpt,
                            isText: isText,
                            size: attachment.size,
                            index: attachmentsList.length
                        });

                        const attachmentIndex = attachmentsList.length - 1;

                        // 根据文件类型添加对应的CSS类
                        let fileTypeClass = '';
                        if (isPdf) fileTypeClass = 'attachment-item-pdf';
                        else if (isWord) fileTypeClass = 'attachment-item-word';
                        else if (isExcel) fileTypeClass = 'attachment-item-excel';
                        else if (isPpt) fileTypeClass = 'attachment-item-powerpoint';
                        else if (isText) fileTypeClass = 'attachment-item-text';
                        else if (/\.zip|\.rar|\.7z$/i.test(attachment.fileName)) fileTypeClass = 'attachment-item-archive';

                        html += `
                        <div class="attachment-item ${isImage ? 'attachment-item-image' : fileTypeClass}" ${isImage ? `style="background-image: url('${downloadUrl}'); background-size: cover; background-position: center;"` : ''} onclick="previewAttachment('${downloadUrl}', '${displayFileName}', ${isImage}, ${isPdf}, ${isWord}, ${isExcel}, ${isPpt}, ${isText}, ${attachmentIndex})" title="${displayFileName}">
                            <div class="attachment-content">
                                ${!isImage ? `<div class="attachment-icon"><i class="fa ${fileIcon} ${getFileIconColor(isPdf, isWord, isExcel, isPpt, isText)}"></i></div>` : ''}
                                <div class="attachment-info ${isImage ? 'attachment-info-image' : ''}">
                                    <div class="attachment-name" title="${displayFileName}">${displayFileName}</div>
                                    <small class="attachment-size">${attachment.size}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    });
                    html += '</div>';

                    // 添加CSS样式
                    if (!document.getElementById('attachment-grid-style')) {
                        const style = document.createElement('style');
                        style.id = 'attachment-grid-style';
                        style.innerHTML = `
                            .attachment-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                                gap: 5px;
                                margin-top: 5px;
                            }

                            @media (max-width: 768px) {
                                .attachment-grid {
                                    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                                }
                            }

                            @media (min-width: 1200px) {
                                .attachment-grid {
                                    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                                }
                            }
                            .attachment-item {
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                padding: 4px;
                                display: flex;
                                flex-direction: column;
                                transition: all 0.3s;
                                position: relative;
                                height: 80px; /* 调整高度为80px，增加高度以适应更多文本 */
                                width: 80px; /* 保持宽度为80px */
                                overflow: hidden;
                                cursor: pointer;
                            }
                            .attachment-item:hover {
                                box-shadow: 0 2px 4px rgba(0,0,0,0.15);
                                transform: translateY(-2px);
                                border-color: #aaa;
                            }
                            .attachment-item-image {
                                padding: 0;
                                background-color: #f8f9fa;
                                position: relative;
                            }
                            .attachment-item-image::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.7) 100%);
                                border-radius: 4px;
                                z-index: 1;
                            }
                            .attachment-content {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                flex-grow: 1;
                                position: relative;
                                z-index: 2;
                            }
                            .attachment-preview {
                                width: 100%;
                                height: 40px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                overflow: hidden;
                                margin-bottom: 2px;
                            }
                            .attachment-image-overlay {
                                width: 100%;
                                height: 100%;
                            }
                            .attachment-icon {
                                font-size: 24px;
                                color: #6c757d;
                                height: 30px;
                                display: flex;
                                align-items: center;
                                margin-bottom: 2px;
                            }
                            .attachment-info {
                                width: 100%;
                                text-align: center;
                                padding: 2px 0;
                            }
                            .attachment-info-image {
                                position: absolute;
                                bottom: 0;
                                left: 0;
                                right: 0;
                                padding: 2px;
                                color: white;
                                text-shadow: 0 1px 2px rgba(0,0,0,0.6);
                            }
                            .attachment-name {
                                font-size: 9px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 100%;
                                display: -webkit-box;
                                -webkit-line-clamp: 2; /* 允许显示2行文本 */
                                -webkit-box-orient: vertical;
                                line-height: 1.2;
                                height: 2.4em; /* 增加高度以容纳2行文本 */
                                word-break: break-all; /* 允许在任何字符处换行 */
                            }
                            .attachment-size {
                                font-size: 8px;
                                color: #6c757d;
                            }
                            .attachment-info-image .attachment-size {
                                color: rgba(255,255,255,0.8);
                            }
                            .attachment-actions {
                                display: flex;
                                justify-content: space-around;
                                margin-top: 2px;
                                padding-top: 2px;
                                border-top: 1px solid #eee;
                                position: relative;
                                z-index: 3;
                                background-color: rgba(255, 255, 255, 0.9);
                            }
                            .attachment-item-image .attachment-actions {
                                position: absolute;
                                bottom: 0;
                                left: 0;
                                right: 0;
                                background: rgba(0,0,0,0.5);
                                border-top: none;
                                padding: 2px;
                                border-radius: 0 0 4px 4px;
                                opacity: 0;
                                transition: opacity 0.2s;
                            }
                            .attachment-item-image:hover .attachment-actions {
                                opacity: 1;
                            }
                            .attachment-item-image .btn-outline-secondary,
                            .attachment-item-image .btn-outline-primary {
                                color: white;
                                border-color: white;
                                background-color: rgba(0,0,0,0.3);
                            }
                            .attachment-item-image .btn-outline-secondary:hover,
                            .attachment-item-image .btn-outline-primary:hover {
                                background-color: rgba(255,255,255,0.2);
                            }
                            .btn-xs {
                                padding: 0.1rem 0.2rem;
                                font-size: 0.65rem;
                                border-radius: 2px;
                                transition: all 0.2s;
                            }

                            .btn-xs:hover {
                                transform: scale(1.05);
                            }
                            
                            /* 文件类型图标颜色 */
                            .file-icon-pdf {
                                color: #e74c3c !important;
                            }
                            .file-icon-word {
                                color: #2b579a !important;
                            }
                            .file-icon-excel {
                                color: #217346 !important;
                            }
                            .file-icon-powerpoint {
                                color: #d24726 !important;
                            }
                            .file-icon-text {
                                color: #3498db !important;
                            }
                            .file-icon-archive {
                                color: #8e44ad !important;
                            }
                            
                            /* 文件类型背景 */
                            .attachment-item-pdf {
                                background-color: rgba(231, 76, 60, 0.1);
                            }
                            .attachment-item-word {
                                background-color: rgba(43, 87, 154, 0.1);
                            }
                            .attachment-item-excel {
                                background-color: rgba(33, 115, 70, 0.1);
                            }
                            .attachment-item-powerpoint {
                                background-color: rgba(210, 71, 38, 0.1);
                            }
                            .attachment-item-text {
                                background-color: rgba(52, 152, 219, 0.1);
                            }
                            .attachment-item-archive {
                                background-color: rgba(142, 68, 173, 0.1);
                            }
                        `;
                        document.head.appendChild(style);
                    }

                    $('#attachmentList').html(html);
                } else {
                    console.warn('没有找到附件或响应无效:', res);
                    $('#attachmentList').html('<div class="alert alert-info">没有附件</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('加载附件失败:', error);
                $('#attachmentList').html('<div class="alert alert-danger">加载附件失败: ' + error + '</div>');
            }
        });
    }

    // 获取文件图标颜色类
    function getFileIconColor(isPdf, isWord, isExcel, isPpt, isText, isArchive) {
        if (isPdf) return 'file-icon-pdf';
        if (isWord) return 'file-icon-word';
        if (isExcel) return 'file-icon-excel';
        if (isPpt) return 'file-icon-powerpoint';
        if (isText) return 'file-icon-text';
        if (isArchive) return 'file-icon-archive';
        return '';
    }

    // 预览附件函数
    function previewAttachment(url, fileName, isImage, isPdf, isWord, isExcel, isPpt, isText, attachmentIndex) {
        console.log('预览附件:', {url, fileName, isImage, isPdf, isWord, isExcel, isPpt, isText, attachmentIndex});
        
        // 创建模态框（如果不存在）
        if (!$('#previewModal').length) {
            $('body').append(`
                <div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="previewModalLabel">
                                    <i class="fa fa-file-o me-2"></i>
                                    <span id="previewModalFileName">附件预览</span>
                                </h5>
                                <div class="attachment-navigation ms-3">
                                    <button class="btn btn-sm btn-outline-secondary" id="prevAttachmentBtn" title="上一个附件">
                                        <i class="fa fa-chevron-left"></i>
                                    </button>
                                    <span class="mx-2" id="attachmentCounter">1/1</span>
                                    <button class="btn btn-sm btn-outline-secondary" id="nextAttachmentBtn" title="下一个附件">
                                        <i class="fa fa-chevron-right"></i>
                                    </button>
                                </div>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body" id="previewModalBody">
                                <!-- 预览内容将在这里动态加载 -->
                                <div class="text-center py-5" id="previewLoadingIndicator">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                    <p class="text-muted">正在加载附件预览...</p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <div class="me-auto" id="previewFileInfo">
                                    <!-- 文件信息将在这里显示 -->
                                </div>
                                <a href="#" class="btn btn-primary me-2" id="previewDownloadBtn" target="_blank">
                                    <i class="fa fa-download"></i> 下载
                                </a>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            
            // 初始化模态框
            $('#previewModal').modal({
                backdrop: true,
                keyboard: true,
                show: false
            });
            
            // 绑定关闭按钮事件
            $('#previewModal').find('[data-dismiss="modal"]').on('click', function() {
                $('#previewModal').modal('hide');
            });
        }
        
        const modal = $('#previewModal');
        const modalBody = $('#previewModalBody');
        const fileInfo = $('#previewFileInfo');
        const downloadBtn = $('#previewDownloadBtn');
        const loadingIndicator = $('#previewLoadingIndicator');

        // 设置当前附件索引
        if (attachmentIndex !== undefined) {
            currentAttachmentIndex = attachmentIndex;
        }

        // 更新附件计数器
        updateAttachmentCounter();

        // 更新导航按钮状态
        updateNavigationButtons();

        // 设置文件名和图标
        $('#previewModalFileName').text(fileName);

        // 设置下载链接
        downloadBtn.attr('href', url);

        // 显示加载指示器
        loadingIndicator.show();
        modalBody.find('.preview-content').remove();

        // 获取文件类型
        const fileExtension = fileName.split('.').pop().toLowerCase();

        // 设置文件类型信息
        let fileTypeInfo = '';
        let fileIcon = 'fa-file-o';

        if (isImage) {
            fileTypeInfo = '图片文件';
            fileIcon = 'fa-file-image-o';
        } else if (isPdf) {
            fileTypeInfo = 'PDF文档';
            fileIcon = 'fa-file-pdf-o';
        } else if (isWord) {
            fileTypeInfo = 'Word文档';
            fileIcon = 'fa-file-word-o';
        } else if (isExcel) {
            fileTypeInfo = 'Excel表格';
            fileIcon = 'fa-file-excel-o';
        } else if (isPpt) {
            fileTypeInfo = 'PowerPoint幻灯片';
            fileIcon = 'fa-file-powerpoint-o';
        } else if (isText) {
            fileTypeInfo = '文本文件';
            fileIcon = 'fa-file-text-o';
        } else {
            fileTypeInfo = '未知文件类型';
        }

        // 更新模态框标题图标
        $('#previewModalLabel i').attr('class', `fa ${fileIcon} me-2`);

        // 显示文件信息
        fileInfo.html(`<span class="badge bg-light text-dark">${fileTypeInfo}</span>`);

        // 显示模态框
        modal.modal('show');

        // 根据文件类型加载预览
        setTimeout(() => {
            try {
                if (isImage) {
                    // 检查是否为HEIC格式
                    const isHeicFormat = /\.(heic|heif)$/i.test(fileName);

                    if (isHeicFormat) {
                        // HEIC格式特殊处理
                        loadingIndicator.hide();
                        modalBody.append(`
                            <div class="preview-content text-center">
                                <div class="alert alert-info mb-3">
                                    <i class="fa fa-info-circle"></i>
                                    <strong>HEIC格式图片</strong> - 这是苹果设备的高效图像格式
                                </div>
                                <div class="heic-preview-container">
                                    <img src="${url}" class="img-fluid" alt="${fileName}"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div class="heic-fallback text-center py-4" style="display: none;">
                                        <div class="mb-4">
                                            <i class="fa fa-file-image-o fa-4x text-muted"></i>
                                        </div>
                                        <h5>${fileName}</h5>
                                        <p class="text-muted">您的浏览器可能不支持HEIC格式预览</p>
                                        <p class="text-muted">建议下载后使用支持HEIC的应用查看</p>
                                        <a href="${url}" class="btn btn-primary" target="_blank" download>
                                            <i class="fa fa-download"></i> 下载图片
                                        </a>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else {
                        // 普通图片预览
                        const img = new Image();
                        img.onload = function() {
                            loadingIndicator.hide();
                            modalBody.append(`
                                <div class="preview-content text-center">
                                    <img src="${url}" class="img-fluid" alt="${fileName}">
                                </div>
                            `);
                        };
                        img.onerror = function() {
                            showPreviewError('图片加载失败');
                        };
                        img.src = url;
                    }

                } else if (isPdf) {
                    // PDF预览
                    console.log('开始加载PDF预览...');
                    
                    // 添加PDF图标和标题样式
                    $('#previewModalLabel i').removeClass().addClass('fa fa-file-pdf-o file-icon-pdf me-2');
                    $('#previewModalLabel').addClass('pdf-title');
                    
                    // 检查PDF.js是否已加载
                    if (typeof pdfjsLib === 'undefined') {
                        console.log('PDF.js未加载，正在动态加载...');
                        
                        // 动态加载PDF.js
                        const script = document.createElement('script');
                        script.src = '/assets/plugins/pdfjs/pdf.js';
                        script.onload = function() {
                            console.log('PDF.js加载成功，配置工作线程...');
                            // 配置PDF.js工作线程
                            pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
                            
                            // 加载完成后重新调用预览函数
                            setTimeout(() => {
                                previewAttachment(url, fileName, isImage, isPdf, isWord, isExcel, isPpt, isText, attachmentIndex);
                            }, 500);
                        };
                        script.onerror = function() {
                            console.error('PDF.js加载失败');
                            showPreviewError('PDF.js库加载失败，无法预览PDF文件');
                        };
                        document.head.appendChild(script);
                        return;
                    }
                    
                    loadingIndicator.hide();

                    // 使用内嵌的PDF.js查看器
                    modalBody.append(`
                        <div class="preview-content">
                            <div class="pdf-container position-relative">
                                <!-- 独立的下载按钮，更加突出 -->
                                <a href="${url}" class="btn btn-primary pdf-download-btn" target="_blank" download>
                                    <i class="fa fa-download"></i> 下载PDF文件
                                </a>
                                <div class="pdf-controls">
                                    <button id="pdf-prev" class="btn btn-sm btn-outline-secondary">
                                        <i class="fa fa-chevron-left"></i> 上一页
                                    </button>
                                    <span class="pdf-page-info">第 <span id="pdf-page-num">1</span> 页 / 共 <span id="pdf-page-count">0</span> 页</span>
                                    <button id="pdf-next" class="btn btn-sm btn-outline-secondary">
                                        下一页 <i class="fa fa-chevron-right"></i>
                                    </button>
                                </div>
                                <div id="pdf-viewer-container" style="width:100%; height:450px; overflow:auto;"></div>
                            </div>
                        </div>
                    `);

                    // 使用PDF.js加载PDF文件
                    try {
                        // 确保URL是完整的
                        let pdfUrl = url;
                        // 如果URL不是以http开头的完整URL，则转换为完整URL
                        if (!pdfUrl.startsWith('http')) {
                            // 获取当前网站的基础URL
                            const baseUrl = window.location.protocol + '//' + window.location.host;
                            // 确保URL以/开头
                            if (!pdfUrl.startsWith('/')) {
                                pdfUrl = '/' + pdfUrl;
                            }
                            pdfUrl = baseUrl + pdfUrl;
                        }
                        
                        // 添加时间戳参数避免缓存问题
                        pdfUrl = pdfUrl + (pdfUrl.includes('?') ? '&' : '?') + '_t=' + new Date().getTime();
                        
                        console.log('加载PDF文件:', pdfUrl);
                        
                        // 添加加载状态提示
                        $('#pdf-viewer-container').html(`
                    <div class="text-center py-4">
                    <div class="spinner-border text-primary mb-3"></div>
                    <p>正在加载PDF文件...</p>
                    </div>
                    `);
                        
                        // 确保PDF.js工作线程已配置
                        if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
                            pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/plugins/pdfjs/pdf.worker.js';
                        }
                        
                        // 尝试使用fetch API先获取PDF数据
                        fetch(pdfUrl, {
                            method: 'GET',
                            credentials: 'include', // 包含cookie等凭证
                            headers: {
                                'Accept': 'application/pdf,*/*',
                                'X-Requested-With': 'XMLHttpRequest',
                                'Cache-Control': 'no-cache, no-store, must-revalidate'
                            }
                        })
                        .then(response => {
                            console.log('PDF Fetch响应状态:', response.status, response.statusText);
                            console.log('PDF Fetch响应头:', JSON.stringify(Array.from(response.headers.entries())));
                            
                            if (!response.ok) {
                                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                            }
                            
                            // 检查内容类型
                            const contentType = response.headers.get('content-type');
                            console.log('PDF内容类型:', contentType);
                            
                            if (contentType && !contentType.includes('application/pdf') && 
                                !contentType.includes('application/octet-stream') && 
                                !contentType.includes('binary/octet-stream')) {
                                console.warn('警告: 服务器返回的不是PDF类型:', contentType);
                            }
                            
                            return response.blob();
                        })
                        .then(blob => {
                            // 检查blob是否为空
                            if (blob.size === 0) {
                                console.error('获取到的PDF blob大小为0');
                                throw new Error('服务器返回了空文件');
                            }
                            
                            console.log('成功获取PDF blob，大小:', blob.size, '字节', '类型:', blob.type);
                            
                            // 将blob转换为ArrayBuffer
                            const reader = new FileReader();
                            reader.onload = function() {
                                const arrayBuffer = this.result;
                                
                                // 检查arrayBuffer是否为空
                                if (!arrayBuffer || arrayBuffer.byteLength === 0) {
                                    console.error('转换后的ArrayBuffer为空');
                                    throw new Error('PDF文件内容为空');
                                }
                                
                                console.log('成功转换为ArrayBuffer，大小:', arrayBuffer.byteLength, '字节');
                                
                                // 使用PDF.js加载ArrayBuffer
                                pdfjsLib.getDocument({data: arrayBuffer}).promise.then(function(pdf) {
                                    console.log('PDF加载成功，页数:', pdf.numPages);
                                    pdfDoc = pdf;
                                    document.getElementById('pdf-page-count').textContent = pdf.numPages;

                                    // 创建PDF查看器
                                    container = document.getElementById('pdf-viewer-container');
                                    container.innerHTML = '';

                                    // 创建canvas
                                    canvas = document.createElement('canvas');
                                    ctx = canvas.getContext('2d');
                                    container.appendChild(canvas);

                                    // 初始渲染第一页
                                    renderPage(pageNum);

                                    // 添加上一页/下一页按钮事件
                                    document.getElementById('pdf-prev').addEventListener('click', onPrevPage);
                                    document.getElementById('pdf-next').addEventListener('click', onNextPage);
                                }).catch(function(error) {
                                    console.error('PDF.js加载错误:', error);
                                    showPreviewError('无法加载PDF文件内容: ' + error.message);
                                });
                            };
                            reader.onerror = function(error) {
                                console.error('FileReader错误:', error);
                                throw new Error('读取PDF文件数据失败');
                            };
                            reader.readAsArrayBuffer(blob);
                        })
                        .catch(error => {
                            console.error('Fetch API错误:', error);
                            // 如果fetch失败，尝试使用XMLHttpRequest直接获取二进制数据
                            console.log('尝试使用XMLHttpRequest获取PDF...');
                            
                            const xhr = new XMLHttpRequest();
                            xhr.open('GET', pdfUrl, true);
                            xhr.responseType = 'arraybuffer';
                            xhr.setRequestHeader('Accept', 'application/pdf,*/*');
                            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                            xhr.setRequestHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
                            
                            xhr.onload = function() {
                                console.log('XMLHttpRequest状态码:', this.status);
                                console.log('XMLHttpRequest响应头:', this.getAllResponseHeaders());
                                
                                if (this.status === 200) {
                                    const arrayBuffer = this.response;
                                    if (!arrayBuffer || arrayBuffer.byteLength === 0) {
                                        console.error('XMLHttpRequest: 获取到的ArrayBuffer为空');
                                        tryFallbackPdfLoading(pdfUrl);
                                        return;
                                    }
                                    
                                    console.log('XMLHttpRequest: 成功获取PDF，大小:', arrayBuffer.byteLength, '字节');
                                    
                                    // 使用PDF.js加载ArrayBuffer
                                    pdfjsLib.getDocument({data: arrayBuffer}).promise.then(function(pdf) {
                                        console.log('XMLHttpRequest: PDF加载成功，页数:', pdf.numPages);
                                        pdfDoc = pdf;
                                        document.getElementById('pdf-page-count').textContent = pdf.numPages;
    
                                        // 创建PDF查看器
                                        container = document.getElementById('pdf-viewer-container');
                                        container.innerHTML = '';
    
                                        // 创建canvas
                                        canvas = document.createElement('canvas');
                                        ctx = canvas.getContext('2d');
                                        container.appendChild(canvas);
    
                                        // 初始渲染第一页
                                        renderPage(pageNum);
    
                                        // 添加上一页/下一页按钮事件
                                        document.getElementById('pdf-prev').addEventListener('click', onPrevPage);
                                        document.getElementById('pdf-next').addEventListener('click', onNextPage);
                                    }).catch(function(error) {
                                        console.error('XMLHttpRequest: PDF.js加载错误:', error);
                                        tryFallbackPdfLoading(pdfUrl);
                                    });
                                } else {
                                    console.error('XMLHttpRequest失败，状态码:', this.status);
                                    tryFallbackPdfLoading(pdfUrl);
                                }
                            };
                            
                            xhr.onerror = function(error) {
                                console.error('XMLHttpRequest请求错误:', error);
                                tryFallbackPdfLoading(pdfUrl);
                            };
                            
                            xhr.send();
                        });
                        
                        // 备用PDF加载方法
                        function tryFallbackPdfLoading(pdfUrl) {
                            console.log('尝试使用备用方法加载PDF');
                            
                            // 尝试直接下载PDF并检查是否存在
                            const checkPdfExistence = new XMLHttpRequest();
                            checkPdfExistence.open('HEAD', pdfUrl, true);
                            checkPdfExistence.onload = function() {
                                console.log('检查PDF是否存在:', this.status, this.statusText);
                                console.log('响应头:', this.getAllResponseHeaders());
                                
                                // 显示服务器返回的详细信息
                                $('#pdf-viewer-container').append(`
                                    <div class="alert alert-warning mb-3">
                                        <strong>服务器响应状态:</strong> ${this.status} ${this.statusText}<br>
                                        <strong>响应头信息:</strong> <pre>${this.getAllResponseHeaders() || '无响应头'}</pre>
                                    </div>
                                `);
                                
                                // 检查服务器是否返回了204状态码（无内容）
                                if (this.status === 204) {
                                    $('#pdf-viewer-container').append(`
                                        <div class="alert alert-danger">
                                            <strong>文件不存在或为空</strong><br>
                                            服务器返回了204状态码（无内容），这通常意味着服务器找不到请求的文件或文件为空。
                                            <hr>
                                            <p>可能的原因：</p>
                                            <ul>
                                                <li>附件文件在服务器上不存在</li>
                                                <li>附件文件存储路径不正确</li>
                                                <li>附件文件名称与请求的不匹配</li>
                                                <li>附件文件内容为空</li>
                                            </ul>
                                            <button class="btn btn-sm btn-primary mt-2" onclick="checkAttachmentExists('${encodeURIComponent(fileName)}')">
                                                检查附件是否存在
                                            </button>
                                        </div>
                                    `);
                                }
                            };
                            checkPdfExistence.onerror = function() {
                                console.error('检查PDF存在性失败');
                            };
                            checkPdfExistence.send();
                            
                            // 替换本地地址为公网地址，确保在线查看器能访问
                            let publicPdfUrl = pdfUrl;
                            publicPdfUrl = publicPdfUrl.replace('http://127.0.0.1:8001/', 'http://enter.theolympiastone.com:8001/');
                            publicPdfUrl = publicPdfUrl.replace('https://127.0.0.1:8001/', 'https://enter.theolympiastone.com:8001/');
                            publicPdfUrl = publicPdfUrl.replace('localhost:8001', 'enter.theolympiastone.com:8001');
                            
                            // 准备在线查看器URL
                            const googlePdfViewerUrl = 'https://docs.google.com/viewer?embedded=true&url=';
                            const microsoftViewerUrl = 'https://view.officeapps.live.com/op/embed.aspx?src=';
                            const encodedPdfUrl = encodeURIComponent(publicPdfUrl);
                            
                            // 生成各种查看器的URL
                            const googleUrl = googlePdfViewerUrl + encodedPdfUrl;
                            const msUrl = microsoftViewerUrl + encodedPdfUrl;
                            
                            // 默认使用Microsoft查看器
                            const defaultViewerUrl = msUrl;
                            
                            // 输出详细日志，便于调试
                            console.log('PDF原始URL:', pdfUrl);
                            console.log('PDF公网URL:', publicPdfUrl);
                            console.log('Microsoft查看器URL:', msUrl);
                            console.log('Google查看器URL:', googleUrl);
                            console.log('直接预览URL:', publicPdfUrl);
                            console.log('默认使用Microsoft查看器');
                            
                            // 在控制台输出可复制的链接
                            console.log('------ 可复制的链接 ------');
                            console.log('Microsoft查看器: ' + msUrl);
                            console.log('Google查看器: ' + googleUrl);
                            console.log('直接访问PDF: ' + publicPdfUrl);
                            console.log('-------------------------');
                            
                            $('#pdf-viewer-container').html(`
                                <div class="alert alert-info mb-3">
                                    <i class="fa fa-info-circle"></i> 
                                    <strong>使用在线PDF查看器</strong>，正在加载PDF预览...
                                </div>
                                <div class="text-center mb-3">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" id="googleViewerBtn">
                                            Google查看器
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary active" id="microsoftViewerBtn">
                                            Microsoft查看器
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" id="browserViewerBtn">
                                            浏览器查看器
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" id="iframeViewerBtn">
                                            直接嵌入
                                        </button>
                                    </div>
                                </div>
                                <div id="pdf-viewer-frame-container" style="width:100%; height:450px;">
                                    <iframe src="${defaultViewerUrl}" width="100%" height="100%" frameborder="0" 
                                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>
                                </div>
                                
                                <div class="alert alert-warning mt-3">
                                    <strong>诊断信息:</strong><br>
                                    <small>服务器返回了状态码204或空内容，这表明PDF文件可能不存在或无法访问。</small>
                                </div>
                            `);
                            
                            // 切换不同的PDF查看器
                            $('#googleViewerBtn').on('click', function() {
                                $(this).addClass('active').siblings().removeClass('active');
                                console.log('切换到Google查看器:', googleUrl);
                                $('#pdf-viewer-frame-container').html(`
                                    <iframe src="${googleUrl}" width="100%" height="100%" frameborder="0" 
                                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>
                                `);
                            });
                            
                            $('#microsoftViewerBtn').on('click', function() {
                                $(this).addClass('active').siblings().removeClass('active');
                                console.log('切换到Microsoft查看器:', msUrl);
                                $('#pdf-viewer-frame-container').html(`
                                    <iframe src="${msUrl}" width="100%" height="100%" frameborder="0" 
                                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>
                                `);
                            });
                            
                            $('#browserViewerBtn').on('click', function() {
                                $(this).addClass('active').siblings().removeClass('active');
                                console.log('切换到浏览器内置查看器');
                                // 使用data URI方案直接在浏览器中打开PDF
                                // 这将触发浏览器的内置PDF查看器
                                $('#pdf-viewer-frame-container').html(`
                                    <object data="${publicPdfUrl}" type="application/pdf" width="100%" height="100%">
                                        <div class="alert alert-warning">
                                            <i class="fa fa-exclamation-triangle"></i> 
                                            您的浏览器不支持内置PDF查看器，请尝试其他查看选项。
                                        </div>
                                    </object>
                                `);
                            });
                            
                            $('#iframeViewerBtn').on('click', function() {
                                $(this).addClass('active').siblings().removeClass('active');
                                console.log('切换到iframe直接嵌入:', publicPdfUrl);
                                $('#pdf-viewer-frame-container').html(`
                                    <iframe src="${publicPdfUrl}" width="100%" height="100%" frameborder="0" 
                                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>
                                `);
                            });
                            
                            // 提供下载链接和测试链接
                            modalBody.append(`
                                <div class="alert alert-info mt-3">
                                    <i class="fa fa-info-circle"></i>
                                    如果PDF无法正常显示，请尝试其他查看器或直接下载查看。
                                </div>
                                <div class="text-center mt-2 mb-2">
                                    <div class="btn-group">
                                        <a href="${url}" class="btn btn-primary" target="_blank" download>
                                            <i class="fa fa-download"></i> 下载PDF
                                        </a>
                                        <button class="btn btn-secondary" onclick="window.open('${publicPdfUrl}', '_blank')">
                                            <i class="fa fa-external-link"></i> 新窗口打开
                                        </button>
                                        <button class="btn btn-info" id="copyMsLinkBtn" data-url="${msUrl}">
                                            <i class="fa fa-copy"></i> 复制MS链接
                                        </button>
                                        <button class="btn btn-info" id="copyGoogleLinkBtn" data-url="${googleUrl}">
                                            <i class="fa fa-copy"></i> 复制Google链接
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="alert alert-secondary mt-3">
                                    <strong>调试信息:</strong>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="testDirectPdfDownload('${url}')">
                                            测试直接下载
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="checkResponseHeaders('${url}')">
                                            检查响应头
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="verifyAttachmentExistence('${emailId}', '${fileName}')">
                                            验证附件存在
                                        </button>
                                    </div>
                                </div>
                            `);
                            
                            // 添加复制链接功能
                            $('#copyMsLinkBtn').on('click', function() {
                                copyToClipboard($(this).data('url'));
                                layer.msg('Microsoft查看器链接已复制到剪贴板', {icon: 1, time: 2000});
                            });
                            
                            $('#copyGoogleLinkBtn').on('click', function() {
                                copyToClipboard($(this).data('url'));
                                layer.msg('Google查看器链接已复制到剪贴板', {icon: 1, time: 2000});
                            });
                            
                            // 复制到剪贴板的辅助函数
                            function copyToClipboard(text) {
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                            }
                            
                            // 添加全局测试函数
                            window.testDirectPdfDownload = function(url) {
                                console.log('测试直接下载PDF:', url);
                                const testFrame = document.createElement('iframe');
                                testFrame.style.display = 'none';
                                testFrame.src = url;
                                document.body.appendChild(testFrame);
                                
                                setTimeout(() => {
                                    document.body.removeChild(testFrame);
                                    layer.msg('测试下载已完成，请检查是否成功下载', {icon: 1});
                                }, 2000);
                            };
                            
                            window.checkResponseHeaders = function(url) {
                                console.log('检查URL响应头:', url);
                                const xhr = new XMLHttpRequest();
                                xhr.open('HEAD', url, true);
                                xhr.onload = function() {
                                    const headers = this.getAllResponseHeaders();
                                    const status = this.status;
                                    
                                    layer.alert(`
                                        <strong>URL:</strong> ${url}<br>
                                        <strong>状态码:</strong> ${status} ${this.statusText}<br>
                                        <strong>响应头:</strong><br>
                                        <pre style="max-height:300px;overflow:auto;">${headers || '无响应头'}</pre>
                                    `, {title: '响应头信息'});
                                };
                                xhr.onerror = function() {
                                    layer.alert(`请求失败，无法获取响应头`, {icon: 2});
                                };
                                xhr.send();
                            };
                            
                            // 验证附件是否存在的函数
                            window.verifyAttachmentExistence = function(emailId, fileName) {
                                console.log('验证附件是否存在:', emailId, fileName);
                                
                                // 显示加载提示
                                const loadingIndex = layer.load(1, {
                                    shade: [0.3, '#fff']
                                });
                                
                                // 调用后端API检查附件是否存在
                                $.ajax({
                                    url: 'admin/emailMessages/checkAttachmentExists',
                                    type: 'GET',
                                    data: {
                                        emailId: emailId,
                                        fileName: fileName
                                    },
                                    dataType: 'json',
                                    success: function(response) {
                                        layer.close(loadingIndex);
                                        
                                        if (response.state === 'ok') {
                                            // 附件存在
                                            layer.alert(`
                                                <div class="text-success">
                                                    <i class="fa fa-check-circle fa-2x"></i>
                                                    <strong>附件文件存在</strong>
                                                </div>
                                                <hr>
                                                <p><strong>文件名:</strong> ${response.data.fileName}</p>
                                                <p><strong>文件大小:</strong> ${response.data.fileSize}</p>
                                                <p><strong>文件路径:</strong> ${response.data.filePath}</p>
                                                <p><strong>最后修改时间:</strong> ${response.data.lastModified}</p>
                                            `, {title: '附件验证结果', area: ['500px', '300px']});
                                        } else {
                                            // 附件不存在
                                            layer.alert(`
                                                <div class="text-danger">
                                                    <i class="fa fa-times-circle fa-2x"></i>
                                                    <strong>附件文件不存在</strong>
                                                </div>
                                                <hr>
                                                <p><strong>错误信息:</strong> ${response.msg}</p>
                                                <p><strong>请求的文件名:</strong> ${fileName}</p>
                                                <p><strong>邮件ID:</strong> ${emailId}</p>
                                                ${response.data && response.data.searchPath ? `<p><strong>搜索路径:</strong> ${response.data.searchPath}</p>` : ''}
                                            `, {title: '附件验证结果', area: ['500px', '300px']});
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        layer.close(loadingIndex);
                                        layer.alert(`
                                            <div class="text-danger">
                                                <i class="fa fa-exclamation-triangle fa-2x"></i>
                                                <strong>验证请求失败</strong>
                                            </div>
                                            <hr>
                                            <p><strong>错误信息:</strong> ${error}</p>
                                            <p><strong>状态码:</strong> ${xhr.status}</p>
                                        `, {title: '附件验证失败', area: ['400px', '250px']});
                                    }
                                });
                            };
                            
                            // 检查附件是否存在的函数（用于204状态码情况）
                            window.checkAttachmentExists = function(fileName) {
                                verifyAttachmentExistence('#(email.id)', decodeURIComponent(fileName));
                            };
                        }
                        
                        let pdfDoc = null;
                        let pageNum = 1;
                        let pageRendering = false;
                        let pageNumPending = null;
                        let scale = 1.5;
                        let container = null;
                        let canvas = null;
                        let ctx = null;

                        // 渲染指定页面
                        function renderPage(num) {
                            pageRendering = true;
                            document.getElementById('pdf-page-num').textContent = num;

                            // 获取页面
                            pdfDoc.getPage(num).then(function(page) {
                                const viewport = page.getViewport({scale: scale});
                                canvas.height = viewport.height;
                                canvas.width = viewport.width;

                                // 渲染PDF页面
                                const renderContext = {
                                    canvasContext: ctx,
                                    viewport: viewport
                                };

                                const renderTask = page.render(renderContext);

                                // 等待渲染完成
                                renderTask.promise.then(function() {
                                    pageRendering = false;
                                    if (pageNumPending !== null) {
                                        // 有新的页面渲染请求
                                        renderPage(pageNumPending);
                                        pageNumPending = null;
                                    }
                                });
                            });

                            // 更新页面导航按钮状态
                            document.getElementById('pdf-prev').disabled = num <= 1;
                            document.getElementById('pdf-next').disabled = num >= pdfDoc.numPages;
                        }

                        // 上一页
                        function onPrevPage() {
                            if (pageNum <= 1) {
                                return;
                            }
                            pageNum--;
                            queueRenderPage(pageNum);
                        }

                        // 下一页
                        function onNextPage() {
                            if (pageNum >= pdfDoc.numPages) {
                                return;
                            }
                            pageNum++;
                            queueRenderPage(pageNum);
                        }

                        // 如果正在渲染另一页，则排队等待
                        function queueRenderPage(num) {
                            if (pageRendering) {
                                pageNumPending = num;
                            } else {
                                renderPage(num);
                            }
                        }

                    } catch (error) {
                        console.error('PDF预览初始化失败:', error);
                        showPreviewError('PDF预览加载失败: ' + error.message);
                    }

                } else if (isWord || isExcel || isPpt) {
                    // Office文档预览
                    // 检测是否为局域网环境
                    const isLocalNetwork = isLocalNetworkEnvironment();

                    if (isLocalNetwork) {
                        // 局域网环境下使用本地预览
                        loadingIndicator.hide();
                        
                        // 添加对应的文件类型图标和样式
                        if (isWord) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-word-o file-icon-word me-2');
                        } else if (isExcel) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-excel-o file-icon-excel me-2');
                        } else if (isPpt) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-powerpoint-o file-icon-powerpoint me-2');
                        }
                        
                        modalBody.append(`
                    <div class="preview-content d-flex flex-column">
                    <div class="text-center py-4">
                    <div class="mb-4">
                    <i class="fa ${fileIcon} fa-4x ${getFileIconColor(isPdf, isWord, isExcel, isPpt, isText)}"></i>
                    </div>
                    <h5>${fileName}</h5>
                    <p class="text-muted mb-4">局域网环境下无法使用在线Office预览</p>
                    <div class="d-flex justify-content-center">
                    <a href="${url}" class="btn btn-primary me-2" target="_blank">
                    <i class="fa fa-download"></i> 下载文件
                    </a>
                    <button class="btn btn-outline-secondary" onclick="tryAlternativePreview('${url}', '${fileName}', ${isWord}, ${isExcel}, ${isPpt})">
                    <i class="fa fa-eye"></i> 尝试替代预览
                    </button>
                    </div>
                    </div>
                    </div>
                    `);
                    } else {
                        // 公网环境下使用Microsoft Office Online服务
                        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(window.location.origin + '/' + url)}`;
                    
                        // 添加对应的文件类型图标和样式
                        if (isWord) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-word-o file-icon-word me-2');
                        } else if (isExcel) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-excel-o file-icon-excel me-2');
                        } else if (isPpt) {
                            $('#previewModalLabel i').removeClass().addClass('fa fa-file-powerpoint-o file-icon-powerpoint me-2');
                        }
                        
                        loadingIndicator.hide();
                        modalBody.append(`
                            <div class="preview-content d-flex flex-column">
                                <iframe src="${previewUrl}" width="100%" height="500px" frameborder="0"
                                    onload="$('#previewLoadingIndicator').hide();"
                                    onerror="showPreviewError('无法加载Office文档')">
                                </iframe>
                                <div class="alert alert-info mt-3">
                                    <i class="fa fa-info-circle"></i>
                                    <strong>注意：</strong> 此预览使用Microsoft Office Online服务。如果预览无法加载，请下载文件后在本地查看。
                                </div>
                            </div>
                        `);
                    }

                } else if (isText) {
                    // 文本文件预览
                    // 添加文本文件图标和标题样式
                    $('#previewModalLabel i').removeClass().addClass('fa fa-file-text-o file-icon-text me-2');
                    $('#previewModalLabel').addClass('text-title');
                    $.ajax({
                        url: url,
                        dataType: 'text',
                        success: function(data) {
                            loadingIndicator.hide();
                            // 对特殊字符进行转义
                            const escapedData = data
                                .replace(/&/g, '&amp;')
                                .replace(/</g, '&lt;')
                                .replace(/>/g, '&gt;')
                                .replace(/"/g, '&quot;')
                                .replace(/'/g, '&\#039;');

                            modalBody.append(`
                                    <div class="preview-content">
                                        <pre style="max-height: 500px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">${escapedData}</pre>
                                    </div>
                                `);
                        },
                        error: function() {
                            showPreviewError('无法加载文本内容');
                        }
                    });

                } else {
                    // 其他类型文件
                    loadingIndicator.hide();
                    
                    // 根据文件扩展名判断文件类型并添加对应图标
                    let fileExtension = fileName.split('.').pop().toLowerCase();
                    let fileTypeIcon = 'fa-file-o';
                    let iconColorClass = '';
                    
                    if (/zip|rar|7z|tar|gz/i.test(fileExtension)) {
                        fileTypeIcon = 'fa-file-archive-o';
                        iconColorClass = 'file-icon-archive';
                        $('#previewModalLabel i').removeClass().addClass('fa fa-file-archive-o file-icon-archive me-2');
                    } else if (/mp3|wav|ogg|flac|aac/i.test(fileExtension)) {
                        fileTypeIcon = 'fa-file-audio-o';
                        iconColorClass = 'text-warning';
                        $('#previewModalLabel i').removeClass().addClass('fa fa-file-audio-o text-warning me-2');
                    } else if (/mp4|avi|mkv|mov|wmv/i.test(fileExtension)) {
                        fileTypeIcon = 'fa-file-video-o';
                        iconColorClass = 'text-danger';
                        $('#previewModalLabel i').removeClass().addClass('fa fa-file-video-o text-danger me-2');
                    } else if (/exe|msi|bin/i.test(fileExtension)) {
                        fileTypeIcon = 'fa-file-code-o';
                        iconColorClass = 'text-secondary';
                        $('#previewModalLabel i').removeClass().addClass('fa fa-file-code-o text-secondary me-2');
                    } else if (/html|htm|xml|js|css|php/i.test(fileExtension)) {
                        fileTypeIcon = 'fa-file-code-o';
                        iconColorClass = 'file-icon-text';
                        $('#previewModalLabel i').removeClass().addClass('fa fa-file-code-o file-icon-text me-2');
                    }
                    
                    modalBody.append(`
                            <div class="preview-content text-center py-5">
                                <div class="mb-4">
                                    <i class="fa ${fileTypeIcon} fa-4x ${iconColorClass}"></i>
                                </div>
                                <h5>${fileName}</h5>
                                <p class="text-muted">此文件类型不支持在线预览</p>
                                <a href="${url}" class="btn btn-primary" target="_blank">
                                    <i class="fa fa-download"></i> 下载文件
                                </a>
                            </div>
                        `);
                }
            } catch (error) {
                showPreviewError('预览加载失败: ' + error.message);
            }
        }, 500); // 延迟加载，显示加载动画
    }

    // 显示预览错误
    function showPreviewError(errorMessage) {
        $('#previewLoadingIndicator').hide();
        $('#previewModalBody').append(`
            <div class="preview-content text-center text-danger py-5">
                <i class="fa fa-exclamation-circle fa-3x mb-3"></i>
                <h5>预览失败</h5>
                <p>${errorMessage}</p>
                <div class="mt-3">
                    <a href="${$('#previewDownloadBtn').attr('href')}" class="btn btn-lg btn-primary" target="_blank" download>
                        <i class="fa fa-download"></i> 下载文件
                    </a>
                    <button onclick="reloadPreview('${$('#previewDownloadBtn').attr('href')}')" class="btn btn-info ml-2">
                        <i class="fa fa-refresh"></i> 重试
                    </button>
                </div>
                <p class="text-muted mt-2">建议下载后在本地查看</p>
            </div>
        `);
    }

    // 重新加载预览
    function reloadPreview(url) {
        if (!url) return;
        // 获取当前附件信息
        if (currentAttachmentIndex >= 0 && attachmentsList[currentAttachmentIndex]) {
            const attachment = attachmentsList[currentAttachmentIndex];
            // 重新预览
            previewAttachment(
                url,
                attachment.fileName,
                attachment.isImage,
                attachment.isPdf,
                attachment.isWord,
                attachment.isExcel,
                attachment.isPpt,
                attachment.isText,
                currentAttachmentIndex
            );
        }
    }

    /**
     * 检测是否为局域网环境
     * 通过检查当前网站的URL来判断
     */
    function isLocalNetworkEnvironment() {
        const hostname = window.location.hostname;

        // 检查是否为局域网IP或本地开发环境
        if (
            hostname === 'localhost' ||
            hostname === '127.0.0.1' ||
            /^192\.168\./.test(hostname) ||
            /^10\./.test(hostname) ||
            /^172\.(1[6-9]|2[0-9]|3[0-1])/.test(hostname) ||
            /^::1$/.test(hostname) ||
            !hostname.includes('.')
        ) {
            return true;
        }

        // 检查是否为内部域名（没有公共后缀）
        const domainParts = hostname.split('.');
        const tld = domainParts[domainParts.length - 1];
        const publicTlds = ['com', 'org', 'net', 'edu', 'gov', 'mil', 'io', 'co', 'info', 'biz', 'cn', 'jp', 'kr', 'uk', 'de', 'fr', 'ru', 'au', 'us'];

        if (!publicTlds.includes(tld.toLowerCase())) {
            return true;
        }

        return false;
    }

    /**
     * 尝试使用替代方法预览Office文档
     */
    function tryAlternativePreview(url, fileName, isWord, isExcel, isPpt) {
        const modalBody = $('#previewModalBody');
        const loadingIndicator = $('#previewLoadingIndicator');

        // 清除当前预览内容
        modalBody.find('.preview-content').remove();
        loadingIndicator.show();

        // 根据文件类型选择预览方式
        if (isWord) {
            // Word文档预览 - 使用文本提取
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'text',
                success: function(data) {
                    loadingIndicator.hide();
                    // 尝试提取文本内容（简单处理）
                    let content = data;
                    // 如果是XML格式（DOCX），尝试提取文本
                    if (content.includes('<w:') || content.includes('<?xml')) {
                        content = "[此Word文档需要专用软件打开]。\n\n请下载文件后在本地查看。";
                    }

                    // 对特殊字符进行转义
                    const escapedContent = content
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;')
                        .replace(/'/g, '&\#039;');

                    modalBody.append(`
                        <div class="preview-content">
                            <div class="alert alert-warning mb-3">
                                <i class="fa fa-info-circle"></i>
                                <strong>注意：</strong> 局域网环境下仅能提供有限的Word文档预览。
                            </div>
                            <div class="card">
                                <div class="card-header bg-light">
                                    <i class="fa fa-file-word-o file-icon-word"></i> ${fileName}
                                </div>
                                <div class="card-body">
                                    <pre style="max-height: 400px; overflow: auto; white-space: pre-wrap; word-wrap: break-word;">${escapedContent}</pre>
                                </div>
                            </div>
                        </div>
                    `);
                },
                error: function() {
                    showPreviewError('无法加载Word文档内容');
                }
            });
        } else if (isExcel) {
            // Excel表格预览 - 显示基本信息
            loadingIndicator.hide();
            modalBody.append(`
                <div class="preview-content">
                    <div class="alert alert-warning mb-3">
                        <i class="fa fa-info-circle"></i>
                        <strong>注意：</strong> 局域网环境下无法预览Excel表格内容。
                    </div>
                    <div class="text-center py-4">
                        <div class="mb-4">
                            <i class="fa fa-file-excel-o fa-4x file-icon-excel"></i>
                        </div>
                        <h5>${fileName}</h5>
                        <p class="text-muted mb-4">请下载文件后在本地查看</p>
                        <a href="${url}" class="btn btn-success" target="_blank" download>
                            <i class="fa fa-download"></i> 下载Excel文件
                        </a>
                    </div>
                </div>
            `);
        } else if (isPpt) {
            // PowerPoint幻灯片预览 - 显示基本信息
            loadingIndicator.hide();
            modalBody.append(`
                <div class="preview-content">
                    <div class="alert alert-warning mb-3">
                        <i class="fa fa-info-circle"></i>
                        <strong>注意：</strong> 局域网环境下无法预览PowerPoint幻灯片内容。
                    </div>
                    <div class="text-center py-4">
                        <div class="mb-4">
                            <i class="fa fa-file-powerpoint-o fa-4x file-icon-powerpoint"></i>
                        </div>
                        <h5>${fileName}</h5>
                        <p class="text-muted mb-4">请下载文件后在本地查看</p>
                        <a href="${url}" class="btn btn-danger" target="_blank" download>
                            <i class="fa fa-download"></i> 下载PowerPoint文件
                        </a>
                    </div>
                </div>
            `);
        }
    }

    /**
     * 在附件之间导航，支持循环浏览
     * @param {string} direction - 'prev' 或 'next'
     */
    function navigateAttachment(direction) {
        if (!attachmentsList.length) return;

        let newIndex = currentAttachmentIndex;

        if (direction === 'prev') {
            // 如果当前是第一个，则循环到最后一个
            if (currentAttachmentIndex <= 0) {
                newIndex = attachmentsList.length - 1;
            } else {
                newIndex = currentAttachmentIndex - 1;
            }
        } else if (direction === 'next') {
            // 如果当前是最后一个，则循环到第一个
            if (currentAttachmentIndex >= attachmentsList.length - 1) {
                newIndex = 0;
            } else {
                newIndex = currentAttachmentIndex + 1;
            }
        }

        // 获取新的附件信息
        const attachment = attachmentsList[newIndex];

        // 预览新附件
        previewAttachment(
            attachment.url,
            attachment.fileName,
            attachment.isImage,
            attachment.isPdf,
            attachment.isWord,
            attachment.isExcel,
            attachment.isPpt,
            attachment.isText,
            newIndex
        );
    }

    /**
     * 更新附件计数器
     */
    function updateAttachmentCounter() {
        if (attachmentsList.length > 0) {
            $('#attachmentCounter').text(`${currentAttachmentIndex + 1}/${attachmentsList.length}`);
        } else {
            $('#attachmentCounter').text('0/0');
        }
    }

    /**
     * 更新导航按钮状态
     */
    function updateNavigationButtons() {
        // 在循环浏览模式下，只要有多于一个附件，按钮始终启用
        if (attachmentsList.length <= 1) {
            // 如果只有一个或没有附件，禁用两个按钮
            $('#prevAttachmentBtn').prop('disabled', true).addClass('disabled');
            $('#nextAttachmentBtn').prop('disabled', true).addClass('disabled');
        } else {
            // 如果有多个附件，启用两个按钮
            $('#prevAttachmentBtn').prop('disabled', false).removeClass('disabled');
            $('#nextAttachmentBtn').prop('disabled', false).removeClass('disabled');
        }

        // 添加视觉提示，当在第一个或最后一个附件时的样式
        if (currentAttachmentIndex <= 0) {
            $('#prevAttachmentBtn').addClass('btn-loop-end');
        } else {
            $('#prevAttachmentBtn').removeClass('btn-loop-end');
        }

        if (currentAttachmentIndex >= attachmentsList.length - 1) {
            $('#nextAttachmentBtn').addClass('btn-loop-end');
        } else {
            $('#nextAttachmentBtn').removeClass('btn-loop-end');
        }
    }

    function downloadAttachment(emailId, fileName) {
        window.location.href = `admin/emailMessages/downloadAttachment?emailId=${emailId}&fileName=${fileName}`;
    }

    function downloadAllAttachments(emailId) {
        window.location.href = `admin/emailMessages/downloadAllAttachments?emailId=${emailId}`;
    }

    /**
     * 删除邮件
     */
    function deleteEmail(emailId) {
        layer.confirm('确定要删除这封邮件吗？删除后无法恢复！', {
            icon: 3,
            title: '删除确认',
            btn: ['确定删除', '取消']
        }, function(index) {
            // 设置删除按钮为加载状态
            const deleteBtn = document.querySelector('button[onclick*="deleteEmail"]');
            if (deleteBtn) {
                deleteBtn.classList.add('loading');
                deleteBtn.disabled = true;
                const icon = deleteBtn.querySelector('i');
                const text = deleteBtn.querySelector('.btn-text');
                if (icon) {
                    icon.className = 'fa fa-spinner fa-spin';
                }
                if (text) {
                    text.textContent = '删除中...';
                }
            }

            // 发送删除请求
            $.ajax({
                url: 'admin/emailMessages/deleteEmail',
                type: 'POST',
                data: { emailId: emailId },
                success: function(res) {
                    if (res.state === 'ok') {
                        layer.msg('邮件删除成功', {icon: 1}, function() {
                            // 删除成功后关闭当前页面或跳转到邮件列表
                            if (window.opener) {
                                // 如果是弹窗打开的，关闭当前窗口并刷新父窗口
                                window.opener.location.reload();
                                window.close();
                            } else {
                                // 如果是直接打开的，跳转到邮件列表
                                window.location.href = 'admin/emailMessages';
                            }
                        });
                    } else {
                        layer.msg('删除失败：' + (res.msg || '未知错误'), {icon: 2});
                        // 恢复按钮状态
                        restoreDeleteButtonState(deleteBtn);
                    }
                },
                error: function() {
                    layer.msg('删除失败，请稍后重试', {icon: 2});
                    // 恢复按钮状态
                    restoreDeleteButtonState(deleteBtn);
                }
            });

            layer.close(index);
        });
    }

    /**
     * 恢复删除按钮状态
     */
    function restoreDeleteButtonState(deleteBtn) {
        if (deleteBtn) {
            deleteBtn.classList.remove('loading');
            deleteBtn.disabled = false;
            const icon = deleteBtn.querySelector('i');
            const text = deleteBtn.querySelector('.btn-text');
            if (icon) {
                icon.className = 'fa fa-trash';
            }
            if (text) {
                text.textContent = '删除邮件';
            }
        }
    }

    /**
     * 邮件重发
     */
    function resendEmail(emailId) {
        layer.confirm('确定要重新发送这封邮件吗？', {
            icon: 3,
            title: '重发确认',
            btn: ['确定重发', '取消']
        }, function(index) {
            // 设置重发按钮为加载状态
            const resendBtn = document.querySelector('button[onclick*="resendEmail"]');
            if (resendBtn) {
                resendBtn.classList.add('loading');
                resendBtn.disabled = true;
                const icon = resendBtn.querySelector('i');
                const text = resendBtn.querySelector('.btn-text');
                if (icon) {
                    icon.className = 'fa fa-spinner fa-spin';
                }
                if (text) {
                    text.textContent = '重发中...';
                }
            }

            // 发送重发请求
            $.ajax({
                url: 'admin/emailMessages/resendEmail',
                type: 'POST',
                data: { emailId: emailId },
                success: function(res) {
                    if (res.state === 'ok') {
                        layer.msg('邮件重发成功', {icon: 1});
                        // 恢复按钮状态
                        restoreResendButtonState(resendBtn);
                    } else {
                        layer.msg('重发失败：' + (res.msg || '未知错误'), {icon: 2});
                        // 恢复按钮状态
                        restoreResendButtonState(resendBtn);
                    }
                },
                error: function() {
                    layer.msg('重发失败，请稍后重试', {icon: 2});
                    // 恢复按钮状态
                    restoreResendButtonState(resendBtn);
                }
            });

            layer.close(index);
        });
    }

    /**
     * 恢复重发按钮状态
     */
    function restoreResendButtonState(resendBtn) {
        if (resendBtn) {
            resendBtn.classList.remove('loading');
            resendBtn.disabled = false;
            const icon = resendBtn.querySelector('i');
            const text = resendBtn.querySelector('.btn-text');
            if (icon) {
                icon.className = 'fa fa-paper-plane';
            }
            if (text) {
                text.textContent = '邮件重发';
            }
        }
    }

    function replyEmail(emailId) {
        // 设置回复按钮为加载状态
        const replyBtn = document.querySelector('button[onclick*="replyEmail"]');
        if (replyBtn) {
            replyBtn.classList.add('loading');
            replyBtn.disabled = true;
            const icon = replyBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }

        try {
            window.open('admin/emailMessages/replyEmail/' + emailId + '?_jb_rqtype_=dialog', '_blank');

            // 延迟恢复按钮状态，给用户足够时间看到反馈
            setTimeout(() => {
                if (replyBtn) {
                    replyBtn.classList.remove('loading');
                    replyBtn.disabled = false;
                    const icon = replyBtn.querySelector('i');
                    if (icon) {
                        icon.className = 'fa fa-reply';
                    }
                }
            }, 1000);
            
        } catch (error) {
            console.error('回复功能出错:', error);
            if (replyBtn) {
                replyBtn.classList.remove('loading');
                replyBtn.disabled = false;
                const icon = replyBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-reply';
                }
            }
        }
    }

    function replyAllEmail(emailId) {
        // 设置回复全部按钮为加载状态
        const replyAllBtn = document.querySelector('button[onclick*="replyAllEmail"]');
        if (replyAllBtn) {
            replyAllBtn.classList.add('loading');
            replyAllBtn.disabled = true;
            const icon = replyAllBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }

        try {
            window.open('admin/emailMessages/replyEmail/' + emailId + '?replyAll=true&_jb_rqtype_=dialog', '_blank');

            // 延迟恢复按钮状态，给用户足够时间看到反馈
            setTimeout(() => {
                if (replyAllBtn) {
                    replyAllBtn.classList.remove('loading');
                    replyAllBtn.disabled = false;
                    const icon = replyAllBtn.querySelector('i');
                    if (icon) {
                        icon.className = 'fa fa-reply-all';
                    }
                }
            }, 1000);

        } catch (error) {
            console.error('回复全部功能出错:', error);
            if (replyAllBtn) {
                replyAllBtn.classList.remove('loading');
                replyAllBtn.disabled = false;
                const icon = replyAllBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-reply-all';
                }
            }
        }
    }

    function forwardEmail(emailId) {
        // 设置转发按钮为加载状态
        const forwardBtn = document.querySelector('button[onclick*="forwardEmail"]');
        if (forwardBtn) {
            forwardBtn.classList.add('loading');
            forwardBtn.disabled = true;
            const icon = forwardBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }
        
        try {
            window.open('admin/emailMessages/forwardEmail/' + emailId + '?_jb_rqtype_=dialog', '_blank');
            
            // 延迟恢复按钮状态
            setTimeout(() => {
                if (forwardBtn) {
                    forwardBtn.classList.remove('loading');
                    forwardBtn.disabled = false;
                    const icon = forwardBtn.querySelector('i');
                    if (icon) {
                        icon.className = 'fa fa-share';
                    }
                }
            }, 1000);
            
        } catch (error) {
            console.error('转发功能出错:', error);
            if (forwardBtn) {
                forwardBtn.classList.remove('loading');
                forwardBtn.disabled = false;
                const icon = forwardBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-share';
                }
            }
        }
    }

    let isTranslated = false;

    /**
     * 设置按钮加载状态
     */
    function setButtonLoading(buttonId, loading = true) {
        const btn = document.getElementById(buttonId);
        if (!btn) return;
        
        if (loading) {
            btn.classList.add('loading');
            btn.disabled = true;
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        } else {
            btn.classList.remove('loading');
            btn.disabled = false;
        }
    }

    /**
     * 重置按钮到原始状态
     */
    function resetButtonState(buttonId, originalIcon, originalText) {
        const btn = document.getElementById(buttonId);
        if (!btn) return;
        
        btn.classList.remove('loading');
        btn.disabled = false;
        
        const icon = btn.querySelector('i');
        const text = btn.querySelector('.btn-text');
        
        if (icon) {
            icon.className = originalIcon;
        }
        if (text) {
            text.textContent = originalText;
        }
    }

    async function openTranslation() {
        // 设置翻译按钮为加载状态
        const translateBtn = document.querySelector('button[onclick*="openTranslation"]');
        if (translateBtn) {
            setButtonLoading('', true);
            translateBtn.classList.add('loading');
            translateBtn.disabled = true;
            const icon = translateBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }
        
        try {
            const content = document.querySelector('.email-preview-body').innerText ||
                document.querySelector('.email-preview-body').textContent;
            
            // 显示加载中提示
            const loadingIndex = layer.msg('正在准备翻译...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });
            
            // 获取窗口尺寸，用于设置弹窗大小
            const winWidth = window.innerWidth * 0.9;  // 窗口宽度的90%
            const winHeight = window.innerHeight * 0.9;  // 窗口高度的90%
            
            layer.close(loadingIndex);
            layer.open({
                type: 2,
                title: '邮件翻译',
                area: [winWidth + 'px', winHeight + 'px'],
                maxmin: true,  // 允许最大化和最小化
                content: 'admin/emailMessages/showTranslation?emailId=#(email.id??)&_jb_rqtype_=dialog',
                success: function(layero, index) {
                    // 确保弹窗内容区域高度合适
                    $(layero).find('.layui-layer-content').css({
                        'overflow': 'auto',
                        'height': (winHeight - 50) + 'px'  // 减去标题栏高度
                    });
                },
                end: function() {
                    // 弹窗关闭时恢复按钮状态
                    if (translateBtn) {
                        translateBtn.classList.remove('loading');
                        translateBtn.disabled = false;
                        const icon = translateBtn.querySelector('i');
                        if (icon) {
                            icon.className = 'fa fa-language';
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('翻译功能出错:', error);
            layer.msg('翻译功能暂时不可用', {icon: 2});
            
            // 恢复按钮状态
            if (translateBtn) {
                translateBtn.classList.remove('loading');
                translateBtn.disabled = false;
                const icon = translateBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-language';
                }
            }
        }
    }

    /**
     * HTML实体解码函数
     * 将HTML实体编码转换为对应的字符或HTML标签
     * @param {string} text - 包含HTML实体的文本
     * @returns {string} - 解码后的文本
     */
    function decodeHtmlEntities(text) {
        if (!text || typeof text !== 'string') return text;

        // 创建一个临时的textarea元素用于解码HTML实体
        const textarea = document.createElement('textarea');

        // 常见HTML实体映射表（用于安全检查和备用解码）
        const entityMap = {
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&\#39;': "'",
            '&apos;': "'",
            '&nbsp;': ' ',
            '&\#32;': ' ',
            '&\#160;': ' ',
            '&copy;': '©',
            '&reg;': '®',
            '&trade;': '™',
            '&hellip;': '…',
            '&mdash;': '—',
            '&ndash;': '–',
            '&lsquo;': '\u2018',
            '&rsquo;': '\u2019',
            '&ldquo;': '\u201C',
            '&rdquo;': '\u201D'
        };

        try {
            // 首先使用浏览器原生的HTML实体解码
            textarea.innerHTML = text;
            let decoded = textarea.value;

            // 如果原生解码失败或不完整，使用手动映射
            if (decoded === text) {
                // 使用正则表达式进行全局替换
                for (const [entity, char] of Object.entries(entityMap)) {
                    const regex = new RegExp(entity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                    decoded = decoded.replace(regex, char);
                }
            }

            return decoded;
        } catch (error) {
            console.warn('HTML实体解码失败:', error);
            // 如果解码失败，至少处理最基本的实体
            let fallbackDecoded = text;
            for (const [entity, char] of Object.entries(entityMap)) {
                const regex = new RegExp(entity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                fallbackDecoded = fallbackDecoded.replace(regex, char);
            }
            return fallbackDecoded;
        }
    }

    /**
     * 检查文本是否包含HTML实体编码
     * @param {string} text - 要检查的文本
     * @returns {boolean} - 是否包含HTML实体
     */
    function containsHtmlEntities(text) {
        if (!text || typeof text !== 'string') return false;

        // 检查常见的HTML实体
        const entityPattern = /&(?:lt|gt|amp|quot|\#39|apos|nbsp|\#\d+|\#x[0-9a-f]+);/i;
        return entityPattern.test(text);
    }

    function isHtmlContent(text) {
        if (!text) return false;

        // 首先检查是否包含HTML实体编码
        if (containsHtmlEntities(text)) {
            // 如果包含HTML实体，先解码再检查
            const decoded = decodeHtmlEntities(text);
            const htmlRegex = /<[a-z][\s\S]*>/i;
            return htmlRegex.test(decoded);
        }

        // 直接检查HTML标签
        const htmlRegex = /<[a-z][\s\S]*>/i;
        return htmlRegex.test(text);
    }

    /**
     * 测试HTML实体解码功能
     * 在浏览器控制台中调用 testHtmlEntityDecoding() 来测试
     */
    function testHtmlEntityDecoding() {
        console.log('=== HTML实体解码功能测试 ===');

        const testCases = [
            '&lt;p&gt;Hello World&lt;/p&gt;',
            '&lt;table&gt;&lt;tr&gt;&lt;td&gt;Cell 1&lt;/td&gt;&lt;td&gt;Cell 2&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;',
            'Company &amp; Partners',
            '&quot;Hello&quot; &amp; &apos;World&apos;',
            '&\#39;Single Quote&\#39; &\#32; &\#160;',
            '&nbsp;&copy;&reg;&trade;',
            '&lt;a href=&quot;http://example.com&quot;&gt;Link&lt;/a&gt;',
            'Normal text without entities',
            '&lt;div style=&quot;color: red;&quot;&gt;Styled content&lt;/div&gt;'
        ];

        testCases.forEach((testCase, index) => {
            console.log(`测试案例 ${index + 1}:`);
            console.log(`原始: ${testCase}`);
            console.log(`包含实体: ${containsHtmlEntities(testCase)}`);
            console.log(`解码后: ${decodeHtmlEntities(testCase)}`);
            console.log(`是HTML: ${isHtmlContent(testCase)}`);
            console.log('---');
        });

        console.log('=== 测试完成 ===');
    }

    // 将测试函数暴露到全局作用域，方便在控制台调用
    window.testHtmlEntityDecoding = testHtmlEntityDecoding;

    /**
     * 安全的HTML内容处理函数
     * 在解码HTML实体后进行安全检查，防止XSS攻击
     * @param {string} content - 要处理的内容
     * @returns {string} - 安全处理后的内容
     */
    function sanitizeDecodedContent(content) {
        if (!content || typeof content !== 'string') return content;

        // 危险的标签和属性列表
        const dangerousTags = [
            'script', 'iframe', 'object', 'embed', 'applet', 'form', 'input',
            'button', 'select', 'textarea', 'meta', 'link', 'base'
        ];

        const dangerousAttributes = [
            'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
            'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
            'javascript:', 'vbscript:', 'data:', 'about:'
        ];

        let sanitized = content;

        // 移除危险的标签
        dangerousTags.forEach(tag => {
            const regex = new RegExp(`<\\/?${tag}[^>]*>`, 'gi');
            sanitized = sanitized.replace(regex, '');
        });

        // 移除危险的属性
        dangerousAttributes.forEach(attr => {
            if (attr.includes(':')) {
                // 处理协议类型的危险属性
                const regex = new RegExp(`${attr.replace(':', '\\s*:\\s*')}[^\\s"'>]*`, 'gi');
                sanitized = sanitized.replace(regex, '');
            } else {
                // 处理事件处理器类型的危险属性
                const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi');
                sanitized = sanitized.replace(regex, '');
            }
        });

        return sanitized;
    }

    /**
     * 增强的HTML实体解码函数（包含安全检查）
     * @param {string} text - 包含HTML实体的文本
     * @returns {string} - 安全解码后的文本
     */
    function safeDecodeHtmlEntities(text) {
        if (!text || typeof text !== 'string') return text;

        // 首先进行HTML实体解码
        const decoded = decodeHtmlEntities(text);

        // 然后进行安全检查和清理
        const sanitized = sanitizeDecodedContent(decoded);

        return sanitized;
    }

    function formatEmailContent(content) {
        if (!content) return '';

        // 首先安全解码HTML实体
        if (containsHtmlEntities(content)) {
            content = safeDecodeHtmlEntities(content);
        }

        if (isHtmlContent(content)) return content;

        // 处理引用部分 - 增强样式
        content = content.replace(/^(>.*?)$/gm, '<div class="quoted-text">$1</div>');

        // 处理签名部分 - 更好的识别
        content = content.replace(/(-{2,}|_{2,})\n([\s\S]*$)/, '<div class="email-signature">$1\n$2</div>');

        // 尝试识别没有明确分隔符的签名
        const signaturePatterns = [
            /\n(Best regards|Regards|Sincerely|Thanks|Thank you|Cheers|BR|Best|Yours)[,\s]\n([\s\S]*$)/i,
            /\n(祝[好|安|顺|健康]|此致|敬礼|谢谢|感谢|BR|问候)\s*[,|，|!|！]*\s*\n([\s\S]*$)/
        ];

        for (const pattern of signaturePatterns) {
            if (pattern.test(content)) {
                content = content.replace(pattern, '\n<div class="email-signature">$1$2</div>');
                break;
            }
        }

        // 识别链接并转换为可点击的链接
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');

        // 移除连续的空行，将多个换行替换为单个换行
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 处理普通换行
        return content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&\#039;')
            .replace(/\n/g, '<br>')
            .replace(/\r/g, '')
            .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
    }

    // 添加图片预览功能
    function previewImage(src) {
        const modal = `
        <div class="modal fade" id="imagePreviewModal" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">图片预览</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${src}" class="img-fluid" style="max-height: 80vh;">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

        // 移除可能存在的旧模态框
        $('#imagePreviewModal').remove();

        // 添加新模态框并显示
        $('body').append(modal);
        const $modal = $('#imagePreviewModal');
        $modal.modal({
            backdrop: true,
            keyboard: true,
            show: true
        });

        // 绑定关闭事件
        $modal.find('[data-dismiss="modal"]').on('click', function () {
            $modal.modal('hide');
        });

        // 模态框隐藏后移除
        $modal.on('hidden.bs.modal', function () {
            $(this).remove();
        });
    }
    /**
     * 切换夜间模式
     */
    function toggleDarkMode() {
        const body = document.body;
        const darkModeBtn = document.getElementById('darkModeToggle');

        if (body.classList.contains('dark-mode')) {
            // 切换到正常模式
            body.classList.remove('dark-mode');
            darkModeBtn.innerHTML = '<i class="fa fa-moon-o"></i> <span class="btn-text">夜间模式</span>';
            darkModeBtn.classList.remove('dark-mode-active');
            localStorage.setItem('emailViewDarkMode', 'false');
        } else {
            // 切换到夜间模式
            body.classList.add('dark-mode');
            darkModeBtn.innerHTML = '<i class="fa fa-sun-o"></i> <span class="btn-text">正常模式</span>';
            darkModeBtn.classList.add('dark-mode-active');
            localStorage.setItem('emailViewDarkMode', 'true');
        }

        // 更新打印模板的样式
        updatePrintTemplateStyle();
    }

    /**
     * 检查用户偏好设置
     */
    function checkDarkModePreference() {
        const darkModePreference = localStorage.getItem('emailViewDarkMode');
        const darkModeBtn = document.getElementById('darkModeToggle');

        // 只有用户明确设置为夜间模式时才启用，默认始终是正常模式
        if (darkModePreference === 'true') {
            document.body.classList.add('dark-mode');
            if (darkModeBtn) {
                darkModeBtn.innerHTML = '<i class="fa fa-sun-o"></i> <span class="btn-text">正常模式</span>';
                darkModeBtn.classList.add('dark-mode-active');
            }
        } else {
            // 确保默认是正常模式
            document.body.classList.remove('dark-mode');
            if (darkModeBtn) {
                darkModeBtn.innerHTML = '<i class="fa fa-moon-o"></i> <span class="btn-text">夜间模式</span>';
                darkModeBtn.classList.remove('dark-mode-active');
            }
        }

        // 注释掉系统偏好设置检查，确保默认始终是正常模式
        // if (darkModePreference === null && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        //     document.body.classList.add('dark-mode');
        //     if (darkModeBtn) {
        //         darkModeBtn.innerHTML = '<i class="fa fa-sun-o"></i> <span class="btn-text">正常模式</span>';
        //         darkModeBtn.classList.add('dark-mode-active');
        //     }
        //     localStorage.setItem('emailViewDarkMode', 'true');
        // }
    }

    /**
     * 查找邮件中的签名表格
     * 通常签名表格有以下特征:
     * 1. 位于邮件末尾
     * 2. 包含联系信息、地址、电话号码等
     * 3. 通常包含少量文本和可能的图片(如LOGO)
     */
    function findSignatureTables(container) {
        if (!container) return [];
        
        const allTables = Array.from(container.querySelectorAll('table'));
        const signatureTables = [];
        
        // 检查每个表格是否符合签名表格特征
        allTables.forEach(table => {
            let isSignature = false;
            const text = table.textContent || '';
            
            // 特征1: 包含常见签名关键词
            const signatureKeywords = [
                'regards', '此致', '敬礼', 'Sincerely', 'Tel:', 'Phone:', 'Email:',
                'Address:', '地址', '电话', '邮箱', 'www.', 'http://', 'https://'
            ];
            
            for (const keyword of signatureKeywords) {
                if (text.toLowerCase().includes(keyword.toLowerCase())) {
                    isSignature = true;
                    break;
                }
            }
            
            // 特征2: 表格位于页面底部(在文档流的后半部分)
            if (!isSignature) {
                const allElements = Array.from(container.querySelectorAll('*'));
                const tableIndex = allElements.indexOf(table);
                if (tableIndex > allElements.length * 0.7) { // 在文档流后70%的位置
                    isSignature = true;
                }
            }
            
            // 特征3: 包含联系信息模式(电话号码、电子邮件等)
            if (!isSignature) {
                const phonePattern = /(\+\d{1,3}[\s\-]?)?\(?\d{3,5}\)?[\s\-]?\d{3,4}[\s\-]?\d{3,4}/;
                const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
                
                if (phonePattern.test(text) || emailPattern.test(text)) {
                    isSignature = true;
                }
            }
            
            if (isSignature) {
                signatureTables.push(table);
            }
        });
        
        return signatureTables;
    }

    /**
     * 改善内容间距，避免内容堆叠
     * @param {HTMLElement} container - 要处理的容器
     */
    function improveContentSpacing(container) {
        // 1. 处理段落和块级元素的间距
        const blockElements = container.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, blockquote');
        blockElements.forEach(el => {
            // 确保块级元素有适当的行高和间距
            if (!el.style.lineHeight) {
                el.style.lineHeight = '1.6';
            }

            // 为段落添加底部间距
            if (el.tagName.toLowerCase() === 'p' && !el.style.marginBottom) {
                el.style.marginBottom = '1em';
            }

            // 为div添加适当间距（如果有内容）
            if (el.tagName.toLowerCase() === 'div' && el.textContent.trim() && !el.style.marginBottom) {
                el.style.marginBottom = '0.5em';
            }
        });

        // 2. 处理文本节点间的间距
        const walker = document.createTreeWalker(
            container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let textNode;
        while (textNode = walker.nextNode()) {
            const parent = textNode.parentElement;
            if (parent && textNode.textContent.trim()) {
                // 确保父元素有适当的行高
                if (!parent.style.lineHeight) {
                    parent.style.lineHeight = '1.5';
                }

                // 检查文本节点是否紧邻其他元素
                const nextSibling = textNode.nextSibling;
                if (nextSibling) {
                    // 如果下一个是元素节点，确保有适当间距
                    if (nextSibling.nodeType === Node.ELEMENT_NODE) {
                        const nextEl = nextSibling;
                        if (['span', 'a', 'strong', 'b', 'em', 'i'].includes(nextEl.tagName.toLowerCase())) {
                            if (!nextEl.style.marginLeft) {
                                nextEl.style.marginLeft = '0.2em';
                            }
                        }
                    }
                    // 如果下一个是文本节点且内容不以空格开头，添加空格
                    else if (nextSibling.nodeType === Node.TEXT_NODE &&
                             nextSibling.textContent.trim() &&
                             !nextSibling.textContent.startsWith(' ')) {
                        nextSibling.textContent = ' ' + nextSibling.textContent;
                    }
                }
            }
        }

        // 3. 处理行内元素的间距
        const inlineElements = container.querySelectorAll('span, a, strong, b, em, i');
        inlineElements.forEach(el => {
            // 为有内容的行内元素添加右边距
            if (el.textContent.trim() && !el.style.marginRight) {
                el.style.marginRight = '0.2em';
            }

            // 确保行内元素不会堆叠
            if (!el.style.display) {
                el.style.display = 'inline';
            }
        });

        // 4. 处理表格内容的间距
        const tableCells = container.querySelectorAll('td, th');
        tableCells.forEach(cell => {
            if (!cell.style.padding) {
                cell.style.padding = '4px 8px';
            }
            if (!cell.style.lineHeight) {
                cell.style.lineHeight = '1.4';
            }
        });

        // 5. 处理换行符，确保它们有效果
        const brs = container.querySelectorAll('br');
        brs.forEach(br => {
            // 为换行符添加最小高度，确保可见
            br.style.lineHeight = '1.5';
            br.style.display = 'block';
            br.style.margin = '0.3em 0';
        });
    }

    /**
     * 修复过度嵌套的元素，避免内容重叠
     * @param {HTMLElement} container - 要处理的容器
     */
    function fixNestedElements(container) {
        // 特殊处理blockquote元素，确保不会被过度清理
        const blockquotes = container.querySelectorAll('blockquote');
        blockquotes.forEach(quote => {
            // 标记blockquote元素，防止被后续处理修改
            quote.setAttribute('data-preserve', 'true');
            
            // 标记blockquote内的所有元素
            const allElements = quote.querySelectorAll('*');
            allElements.forEach(el => {
                el.setAttribute('data-in-blockquote', 'true');
            });
        });
        
        // 标记邮件签名表格，避免被过度处理
        const signatureTables = findSignatureTables(container);
        signatureTables.forEach(table => {
            // 标记签名表格，防止被后续处理修改
            table.setAttribute('data-signature-table', 'true');
            
            // 标记签名表格内的所有元素
            const allElements = table.querySelectorAll('*');
            allElements.forEach(el => {
                el.setAttribute('data-in-signature', 'true');
            });
        });
        
        // 1. 查找过度嵌套的内容（同一级别内嵌套相同标签的情况）
        const nestedSpans = container.querySelectorAll('span span');
        nestedSpans.forEach(span => {
            // 如果在blockquote或签名表格内，跳过处理
            if (span.getAttribute('data-in-blockquote') === 'true' || span.getAttribute('data-in-signature') === 'true') {
                return;
            }

            // 检查父子span是否有相同的样式
            const parentSpan = span.parentElement;
            if (parentSpan.tagName.toLowerCase() === 'span') {
                // 改进：为嵌套span添加适当的间距，避免内容堆叠
                if (!span.style.marginRight) {
                    span.style.marginRight = '0.2em';
                }

                // 如果span内容为空或只有空格，添加最小宽度
                if (!span.textContent.trim()) {
                    span.style.minWidth = '0.5em';
                    span.style.display = 'inline-block';
                }

                // 检查是否有完全相同的样式
                if (span.style && parentSpan.style) {
                    if (span.getAttribute('style') === parentSpan.getAttribute('style')) {
                        // 只移除重复的样式，但保留元素本身
                        span.removeAttribute('style');
                        span.style.marginRight = '0.2em'; // 重新添加间距
                    }
                }
            }
        });
        
        // 2. 处理嵌套的div - 改善显示效果
        const nestedDivs = container.querySelectorAll('div div');
        nestedDivs.forEach(div => {
            // 如果在blockquote或签名表格内，跳过处理
            if (div.getAttribute('data-in-blockquote') === 'true' || div.getAttribute('data-in-signature') === 'true') {
                return;
            }

            const parentDiv = div.parentElement;
            if (parentDiv.tagName.toLowerCase() === 'div') {
                // 为嵌套div添加适当的间距，避免内容堆叠
                if (!div.style.marginBottom) {
                    div.style.marginBottom = '0.3em';
                }

                // 确保div有适当的行高
                if (!div.style.lineHeight) {
                    div.style.lineHeight = '1.5';
                }

                // 仅当div为空时才移除，避免丢失内容
                if (div.childNodes.length === 0) {
                    div.remove();
                }
            }
        });
        
        // 3. 修复邮件地址标签问题（如<<EMAIL>>被错误解析为HTML标签）
        const allElements = container.querySelectorAll('*');
        allElements.forEach(el => {
            // 如果在blockquote或签名表格内，跳过处理
            if (el.getAttribute('data-in-blockquote') === 'true' || el.getAttribute('data-in-signature') === 'true') {
                return;
            }
            
            // 检查标签名是否看起来像邮件地址
            const tagName = el.tagName.toLowerCase();
            if (tagName.includes('@') || tagName.includes('.')) {
                // 这可能是一个被错误解析的邮件地址
                // 创建一个文本节点替换它
                const textNode = document.createTextNode('<' + tagName + '>');
                el.parentNode.replaceChild(textNode, el);
            }
        });
        
        // 4. 处理嵌套的p标签 - 改善段落显示
        const nestedPs = container.querySelectorAll('p p');
        nestedPs.forEach(p => {
            // 如果在blockquote或签名表格内，跳过处理
            if (p.getAttribute('data-in-blockquote') === 'true' || p.getAttribute('data-in-signature') === 'true') {
                return;
            }

            const parentP = p.parentElement;
            if (parentP.tagName.toLowerCase() === 'p') {
                // 为嵌套p标签添加适当的间距，避免内容堆叠
                if (!p.style.marginTop) {
                    p.style.marginTop = '0.5em';
                }
                if (!p.style.marginBottom) {
                    p.style.marginBottom = '0.5em';
                }

                // 确保p标签有适当的行高
                if (!p.style.lineHeight) {
                    p.style.lineHeight = '1.6';
                }

                // 只处理空的嵌套p标签
                if (p.childNodes.length === 0) {
                    p.remove();
                }
            }
        });
        
        // 移除临时标记属性
        const markedElements = container.querySelectorAll('[data-preserve], [data-in-blockquote], [data-signature-table], [data-in-signature]');
        markedElements.forEach(el => {
            el.removeAttribute('data-preserve');
            el.removeAttribute('data-in-blockquote');
            el.removeAttribute('data-signature-table');
            el.removeAttribute('data-in-signature');
        });
    }

    /**
     * 修复邮件引用中错误的HTML标签
     */
    function fixEmailQuoteMarkup(container) {
        // 查找所有文本节点
        const walker = document.createTreeWalker(
            container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const nodesToFix = [];
        while (walker.nextNode()) {
            const node = walker.currentNode;
            const text = node.nodeValue;
            
            // 检查是否包含可能被错误解析的邮件地址格式
            if (text && (text.includes('<') || text.includes('>'))) {
                // 匹配可能的邮件地址模式，如 <<EMAIL>>
                if (/\<[^\>]*@[^\>]*\>/.test(text)) {
                    nodesToFix.push(node);
                }
            }
        }
        
        // 修复标识出的节点
        nodesToFix.forEach(node => {
            let text = node.nodeValue;
            // 转义<和>符号，防止被解析为HTML标签
            text = text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            
            // 创建一个新的文本节点
            const newNode = document.createTextNode(text);
            node.parentNode.replaceChild(newNode, node);
        });
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 检查夜间模式偏好
        checkDarkModePreference();
        
        // 初始化按钮状态
        initializeButtons();
        
        // 检查AI排版状态
        checkAITypesetStatus();
        
        // 其他初始化逻辑...
        console.log('邮件详情页面初始化完成');
    });

    /**
     * 初始化按钮状态
     */
    function initializeButtons() {
        // 确保所有按钮处于正确的初始状态
        const actionButtons = document.querySelectorAll('.btn-action');
        actionButtons.forEach(btn => {
            btn.classList.remove('loading');
            btn.disabled = false;
        });
        
        // 检查强制重新获取按钮是否需要显示
        const fetchStatus = document.getElementById('fetchStatus');
        const forceRefetchBtn = document.getElementById('forceRefetchBtn');
        
        if (fetchStatus && forceRefetchBtn) {
            const status = fetchStatus.value;
            if (status === 'quick_receive' || status === 'partial') {
                forceRefetchBtn.style.display = 'inline-block';
            }
        }
        
        console.log('按钮状态初始化完成');
    }

    /**
     * 添加键盘快捷键支持
     */
    document.addEventListener('keydown', function(e) {
        // 检查是否有模态框打开，如果有则不处理快捷键
        if (document.querySelector('.modal.show')) {
            return;
        }
        
        // Alt + R: 回复
        if (e.altKey && e.key === 'r') {
            e.preventDefault();
            const emailId = document.getElementById('emailId')?.value;
            if (emailId) {
                replyEmail(emailId);
            }
        }
        
        // Alt + F: 转发
        if (e.altKey && e.key === 'f') {
            e.preventDefault();
            const emailId = document.getElementById('emailId')?.value;
            if (emailId) {
                forwardEmail(emailId);
            }
        }
        
        // Alt + T: 翻译
        if (e.altKey && e.key === 't') {
            e.preventDefault();
            openTranslation();
        }
        
        // Alt + D: 切换夜间模式
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            toggleDarkMode();
        }
        
        // Alt + P: 打印
        if (e.altKey && e.key === 'p') {
            e.preventDefault();
            printEmail();
        }
        
        // Alt + A: AI排版
        if (e.altKey && e.key === 'a') {
            e.preventDefault();
            aiTypesetting();
        }
    });

    /**
     * AI排版邮件内容
     */
    function aiTypesetting() {
        // 检查是否已经排版过
        const aiBtn = document.querySelector('button[onclick*="aiTypesetting"]');
        const isAlreadyTypeset = aiBtn && aiBtn.classList.contains('ai-typeset-active');
        
        // 显示确认对话框
        const confirmMsg = isAlreadyTypeset 
            ? '此邮件已进行过AI排版，是否要重新排版？'
            : '确定要使用AI对邮件内容进行排版优化吗？此操作将替换当前显示的内容。';
            
        layer.confirm(confirmMsg, {
            title: 'AI排版确认',
            icon: 3,
            btn: ['确定', '取消']
        }, function(index) {
            layer.close(index);
            performAITypesetting();
        });
    }

    /**
     * 执行AI排版操作
     */
    function performAITypesetting() {
        // 设置AI排版按钮为加载状态
        const aiBtn = document.querySelector('button[onclick*="aiTypesetting"]');
        if (aiBtn) {
            aiBtn.classList.add('loading');
            aiBtn.disabled = true;
            const icon = aiBtn.querySelector('i');
            if (icon) {
                icon.className = 'fa fa-spinner';
            }
        }
        
        try {
            const emailId = document.getElementById('emailId')?.value;
            if (!emailId) {
                layer.msg('无法获取邮件ID', {icon: 2});
                return;
            }
            
            // 显示加载提示
            const loadingIndex = layer.msg('正在进行AI排版，请稍候...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });
            
            // 调用后端API
            $.ajax({
                url: '/admin/emailMessages/aiTypesetting',
                type: 'POST',
                data: { emailId: emailId },
                success: function(response) {
                    layer.close(loadingIndex);
                    
                    if (response.state === 'ok') {
                        // AI排版成功，更新页面内容
                        const originalContent = document.getElementById('originalContent');
                        if (originalContent && response.data) {
                            originalContent.innerHTML = response.data;
                            
                            // 重新处理邮件内容显示
                            handleExternalImages();
                            optimizeEmailDisplay(originalContent);
                            
                            layer.msg('AI排版完成', {icon: 1});
                            
                            // 标记为已排版（可选：添加视觉指示）
                            markAsAITypeset();
                        } else {
                            layer.msg('排版内容获取失败', {icon: 2});
                        }
                    } else {
                        layer.msg(response.msg || 'AI排版失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('AI排版请求失败，请检查网络连接', {icon: 2});
                },
                complete: function() {
                    // 恢复按钮状态
                    if (aiBtn) {
                        aiBtn.classList.remove('loading');
                        aiBtn.disabled = false;
                        const icon = aiBtn.querySelector('i');
                        if (icon) {
                            icon.className = 'fa fa-magic';
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('AI排版功能出错:', error);
            layer.msg('AI排版功能暂时不可用', {icon: 2});
            
            // 恢复按钮状态
            if (aiBtn) {
                aiBtn.classList.remove('loading');
                aiBtn.disabled = false;
                const icon = aiBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fa fa-magic';
                }
            }
        }
    }

    /**
     * 标记邮件为AI排版状态
     */
    function markAsAITypeset() {
        const aiBtn = document.querySelector('button[onclick*="aiTypesetting"]');
        if (aiBtn) {
            aiBtn.classList.add('ai-typeset-active');
            const btnText = aiBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.textContent = '已排版';
            }
            aiBtn.title = '此邮件已进行AI排版优化 (Alt+A 重新排版)';
        }
    }

    /**
     * 检查AI排版状态
     */
    function checkAITypesetStatus() {
        const emailId = document.getElementById('emailId')?.value;
        if (!emailId) return;
        
        // 检查邮件是否已经进行过AI排版
        $.ajax({
            url: '/admin/emailMessages/checkAITypesetStatus',
            type: 'GET',
            data: { emailId: emailId },
            success: function(response) {
                if (response.state === 'ok' && response.data === true) {
                    markAsAITypeset();
                }
            },
            error: function() {
                console.log('检查AI排版状态失败');
            }
        });
    }
</script>

<!-- 附件预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">
                    <i class="fa fa-file-o me-2"></i>
                    <span id="previewModalFileName">附件预览</span>
                </h5>
                <div class="attachment-navigation ms-3">
                    <button class="btn btn-sm btn-outline-secondary" id="prevAttachmentBtn" title="上一个附件">
                        <i class="fa fa-chevron-left"></i>
                    </button>
                    <span class="mx-2" id="attachmentCounter">1/1</span>
                    <button class="btn btn-sm btn-outline-secondary" id="nextAttachmentBtn" title="下一个附件">
                        <i class="fa fa-chevron-right"></i>
                    </button>
                </div>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="previewModalBody">
                <!-- 预览内容将在这里动态加载 -->
                <div class="text-center py-5" id="previewLoadingIndicator">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载附件预览...</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="me-auto" id="previewFileInfo">
                    <!-- 文件信息将在这里显示 -->
                </div>
                <a href="#" class="btn btn-primary me-2" id="previewDownloadBtn" target="_blank">
                    <i class="fa fa-download"></i> 下载
                </a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
    // 页面加载完成后初始化时间本地化
    document.addEventListener('DOMContentLoaded', function() {
        initDateTimeLocalization();
    });
    
    // 如果DOMContentLoaded已经触发，直接调用
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDateTimeLocalization);
    } else {
        initDateTimeLocalization();
    }
</script>
#end